#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Config Master - <PERSON><PERSON><PERSON><PERSON> lý c<PERSON>u hình tổng hợp
<PERSON><PERSON> chức năng từ config.py, config_manager.py, python_version_manager.py, register_pdf_context_menu.py
"""

import os
import sys
import json
import subprocess
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import winreg
import threading
import time
from pathlib import Path
import ctypes
import glob

# Import cho kiểm tra kết nối AI
try:
    import google.generativeai as genai
    from google.cloud import vision
    from google.oauth2 import service_account
    import gspread
    import openai
    from groq import Groq
except ImportError as e:
    print(f"Cảnh báo: Một số thư viện chưa được cài đặt: {e}")

class ConfigMaster:
    def __init__(self):
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.config_file = os.path.join(self.script_dir, 'master_config.json')
        self.requirements_file = os.path.join(self.script_dir, 'requirements.txt')
        
        # Load default config
        self.default_config = {
            "ai_provider": "google",
            "models": {
                "google": {
                    "selected": "gemini-1.5-flash",
                    "available": [
                        "gemini-1.5-flash",
                        "gemini-1.5-pro",
                        "gemini-pro",
                        "gemini-pro-vision"
                    ]
                },
                "openai": {
                    "selected": "gpt-4o-mini",
                    "available": [
                        "gpt-4o",
                        "gpt-4o-mini",
                        "gpt-4-turbo",
                        "gpt-4",
                        "gpt-3.5-turbo"
                    ]
                },
                "grok": {
                    "selected": "grok-beta",
                    "available": [
                        "grok-beta",
                        "grok-vision-beta",
                        "grok-2",
                        "grok-1.5"
                    ]
                }
            },
            "api_keys": {
                "google": "",
                "openai": "",
                "grok": ""
            },
            "google_sheet": {
                "url": "",
                "name": "",
                "credentials_file": ""
            },
            "python_versions": {},
            "selected_python": "",
            "context_menu": {
                "enabled": False,
                "menu_text": "Đổi tên PDF và lưu Google Sheet",
                "icon_path": "",
                "use_vbs": False
            },
            "libraries": {
                "installed": [],
                "missing": []
            }
        }
        
        self.config = self.default_config.copy()
        self.load_config()
        self.detect_python_versions()
        
    def load_config(self):
        """Load cấu hình từ file"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    self.update_config_recursive(self.config, loaded_config)
            except Exception as e:
                print(f"Lỗi khi đọc config: {e}")
        
    def update_config_recursive(self, target, source):
        """Cập nhật config một cách đệ quy"""
        for key, value in source.items():
            if key in target:
                if isinstance(value, dict) and isinstance(target[key], dict):
                    self.update_config_recursive(target[key], value)
                else:
                    target[key] = value
            else:
                target[key] = value
                
    def save_config(self):
        """Lưu cấu hình vào file"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"Lỗi khi lưu config: {e}")
            return False
            
    def detect_python_versions(self):
        """Tự động phát hiện các phiên bản Python"""
        python_versions = {}
        
        # Kiểm tra Python trong PATH
        try:
            result = subprocess.run(['python', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                version = result.stdout.strip()
                python_versions[f"{version} (PATH)"] = {
                    'path': 'python',
                    'auto_detected': True
                }
        except:
            pass
            
        # Kiểm tra các đường dẫn phổ biến
        common_paths = [
            r"C:\Python*\python.exe",
            r"C:\Users\<USER>\AppData\Local\Programs\Python\Python*\python.exe",
            r"C:\Program Files\Python*\python.exe",
            r"C:\Program Files (x86)\Python*\python.exe"
        ]
        
        import glob
        for pattern in common_paths:
            for path in glob.glob(pattern):
                if os.path.exists(path):
                    try:
                        result = subprocess.run([path, '--version'], capture_output=True, text=True)
                        if result.returncode == 0:
                            version = result.stdout.strip()
                            name = f"{version} ({os.path.dirname(path)})"
                            python_versions[name] = {
                                'path': path,
                                'auto_detected': True
                            }
                    except:
                        continue
        
        # Cập nhật config
        if 'python_versions' not in self.config:
            self.config['python_versions'] = {}
            
        # Thêm các phiên bản mới phát hiện
        for name, info in python_versions.items():
            if name not in self.config['python_versions']:
                self.config['python_versions'][name] = info
                
    def check_libraries(self, python_path=None):
        """Kiểm tra thư viện đã cài đặt"""
        if not python_path:
            python_path = self.get_selected_python_path()
            
        if not python_path:
            return [], []
            
        # Đọc requirements.txt
        required_libs = []
        if os.path.exists(self.requirements_file):
            with open(self.requirements_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        # Lấy tên package (bỏ version)
                        lib_name = line.split('>=')[0].split('==')[0].split('<')[0].split('>')[0]
                        required_libs.append(lib_name.strip())
        
        # Kiểm tra thư viện đã cài đặt
        installed = []
        missing = []
        
        for lib in required_libs:
            try:
                result = subprocess.run([python_path, '-c', f'import {lib}'], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    installed.append(lib)
                else:
                    missing.append(lib)
            except:
                missing.append(lib)
                
        self.config['libraries']['installed'] = installed
        self.config['libraries']['missing'] = missing
        
        return installed, missing
        
    def install_libraries(self, python_path=None, progress_callback=None):
        """Cài đặt thư viện thiếu"""
        if not python_path:
            python_path = self.get_selected_python_path()
            
        if not python_path or not os.path.exists(self.requirements_file):
            return False, "Không tìm thấy Python hoặc requirements.txt"
            
        try:
            # Upgrade pip trước
            if progress_callback:
                progress_callback("Đang nâng cấp pip...")
                
            subprocess.run([python_path, '-m', 'pip', 'install', '--upgrade', 'pip'], 
                         check=True, capture_output=True)
            
            # Cài đặt requirements
            if progress_callback:
                progress_callback("Đang cài đặt thư viện...")
                
            result = subprocess.run([python_path, '-m', 'pip', 'install', '-r', self.requirements_file], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                # Cập nhật lại danh sách thư viện
                self.check_libraries(python_path)
                return True, "Cài đặt thành công!"
            else:
                return False, f"Lỗi cài đặt: {result.stderr}"
                
        except Exception as e:
            return False, f"Lỗi: {str(e)}"
            
    def get_selected_python_path(self):
        """Lấy đường dẫn Python đã chọn"""
        selected = self.config.get('selected_python', '')
        if selected and selected in self.config['python_versions']:
            return self.config['python_versions'][selected]['path']
        return None
        
    def register_context_menu(self):
        """Đăng ký context menu cho PDF"""
        try:
            python_path = self.get_selected_python_path()
            if not python_path:
                return False, "Chưa chọn phiên bản Python"
                
            rename_script = os.path.join(self.script_dir, 'rename_pdf.py')
            if not os.path.exists(rename_script):
                return False, "Không tìm thấy rename_pdf.py"
            
            # Tạo key cho PDF files
            pdf_key_path = r"*\shell\RenamePDF"
            
            # Tạo key chính
            with winreg.CreateKey(winreg.HKEY_CLASSES_ROOT, pdf_key_path) as key:
                winreg.SetValue(key, "", winreg.REG_SZ, self.config['context_menu']['menu_text'])
                
                # Thêm icon nếu có
                icon_path = self.config['context_menu']['icon_path']
                if icon_path and os.path.exists(icon_path):
                    winreg.SetValueEx(key, "Icon", 0, winreg.REG_SZ, icon_path)
            
            # Tạo command key
            command_key_path = pdf_key_path + r"\command"
            with winreg.CreateKey(winreg.HKEY_CLASSES_ROOT, command_key_path) as key:
                # Sử dụng pythonw.exe để ẩn terminal
                pythonw_path = python_path.replace('python.exe', 'pythonw.exe')
                if not os.path.exists(pythonw_path):
                    pythonw_path = python_path
                    
                command = f'"{pythonw_path}" "{rename_script}" "%1"'
                winreg.SetValue(key, "", winreg.REG_SZ, command)
            
            self.config['context_menu']['enabled'] = True
            return True, "Đã đăng ký context menu thành công!"
            
        except Exception as e:
            return False, f"Lỗi khi đăng ký: {str(e)}"
            
    def unregister_context_menu(self):
        """Hủy đăng ký context menu"""
        try:
            pdf_key_path = r"*\shell\RenamePDF"
            winreg.DeleteKey(winreg.HKEY_CLASSES_ROOT, pdf_key_path + r"\command")
            winreg.DeleteKey(winreg.HKEY_CLASSES_ROOT, pdf_key_path)
            
            self.config['context_menu']['enabled'] = False
            return True, "Đã hủy đăng ký context menu thành công!"
            
        except Exception as e:
            return False, f"Lỗi khi hủy đăng ký: {str(e)}"
            
    def is_admin(self):
        """Kiểm tra quyền admin"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
            
    def run_as_admin(self):
        """Chạy lại với quyền admin"""
        if self.is_admin():
            return True

        try:
            ctypes.windll.shell32.ShellExecuteW(
                None, "runas", sys.executable, " ".join(sys.argv), None, 1
            )
            return True
        except:
            return False

    def check_google_api_connection(self):
        """Kiểm tra kết nối Google API (Gemini)"""
        try:
            api_key = self.config.get('api_keys', {}).get('google', '')
            if not api_key:
                return False, "Chưa cấu hình Google API Key"

            # Kiểm tra Gemini API
            genai.configure(api_key=api_key)
            model = genai.GenerativeModel("gemini-1.5-flash")
            response = model.generate_content("Hi")

            return True, "Kết nối Google API thành công"
        except Exception as e:
            return False, f"Lỗi kết nối Google API: {str(e)}"

    def check_google_vision_connection(self):
        """Kiểm tra kết nối Google Vision API"""
        try:
            credentials_file = self.config.get('google_sheet', {}).get('credentials_file', '')
            if not credentials_file:
                return False, "Chưa cấu hình file credentials"

            if not os.path.exists(credentials_file):
                return False, f"Không tìm thấy file credentials: {credentials_file}"

            # Tạo credentials từ file JSON
            creds = service_account.Credentials.from_service_account_file(credentials_file)

            # Kiểm tra Vision API với credentials
            client = vision.ImageAnnotatorClient(credentials=creds)

            return True, "Kết nối Google Vision API thành công"
        except Exception as e:
            return False, f"Lỗi kết nối Google Vision API: {str(e)}"

    def check_openai_connection(self):
        """Kiểm tra kết nối OpenAI API"""
        try:
            api_key = self.config.get('api_keys', {}).get('openai', '')
            if not api_key:
                return False, "Chưa cấu hình OpenAI API Key"

            # Khởi tạo client
            client = openai.OpenAI(api_key=api_key)

            # Test với model phổ biến
            response = client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": "Hi"}],
                max_tokens=5,
                timeout=15
            )

            if response and response.choices and len(response.choices) > 0:
                return True, "Kết nối OpenAI API thành công"
            else:
                return False, "Không nhận được phản hồi từ OpenAI"

        except Exception as e:
            error_msg = str(e)
            if "authentication" in error_msg.lower() or "invalid api key" in error_msg.lower():
                return False, "API Key không hợp lệ"
            elif "rate limit" in error_msg.lower():
                return False, "Đã vượt quá rate limit"
            else:
                return False, f"Lỗi kết nối OpenAI API: {error_msg}"

    def check_grok_connection(self):
        """Kiểm tra kết nối Grok API"""
        try:
            api_key = self.config.get('api_keys', {}).get('grok', '')
            if not api_key:
                return False, "Chưa cấu hình Grok API Key"

            # Khởi tạo client
            client = Groq(api_key=api_key)

            # Test với model
            response = client.chat.completions.create(
                model="grok-beta",
                messages=[{"role": "user", "content": "Hi"}],
                max_tokens=5,
                timeout=15
            )

            if response and response.choices and len(response.choices) > 0:
                return True, "Kết nối Grok API thành công"
            else:
                return False, "Không nhận được phản hồi từ Grok"

        except Exception as e:
            error_msg = str(e)
            if "authentication" in error_msg.lower() or "invalid api key" in error_msg.lower():
                return False, "Grok API Key không hợp lệ"
            else:
                return False, f"Lỗi kết nối Grok API: {error_msg}"

    def check_google_sheet_connection(self):
        """Kiểm tra kết nối Google Sheet"""
        try:
            credentials_file = self.config.get('google_sheet', {}).get('credentials_file', '')
            sheet_url = self.config.get('google_sheet', {}).get('url', '')

            if not os.path.exists(credentials_file):
                return False, f"Không tìm thấy file credentials: {credentials_file}"

            if not sheet_url:
                return False, "Chưa cấu hình URL Google Sheet"

            gc = gspread.service_account(filename=credentials_file)
            if sheet_url.startswith('http'):
                sh = gc.open_by_url(sheet_url)
            else:
                sh = gc.open_by_key(sheet_url)

            # Cập nhật tên sheet vào config
            self.config['google_sheet']['name'] = sh.title

            return True, f"Kết nối Google Sheet thành công: {sh.title}"
        except Exception as e:
            return False, f"Lỗi kết nối Google Sheet: {str(e)}"

    def get_latest_models(self, provider):
        """Lấy danh sách model mới nhất cho provider"""
        # Danh sách model mới nhất (cập nhật thường xuyên)
        latest_models = {
            "google": [
                "gemini-2.5-pro-preview-06-05",
                "gemini-2.5-flash-preview-05-20",
                "gemini-2.0-flash",
                "gemini-2.0-flash-lite",
                "gemini-1.5-pro",
                "gemini-1.5-flash",
                "gemini-pro",
                "gemini-pro-vision"
            ],
            "openai": [
                "gpt-4o",
                "gpt-4o-mini",
                "gpt-4-turbo",
                "gpt-4",
                "gpt-3.5-turbo",
                "o1-preview",
                "o1-mini"
            ],
            "grok": [
                "grok-3",
                "grok-3-vision",
                "grok-beta",
                "grok-vision-beta",
                "grok-2-1212",
                "grok-2-vision-1212",
                "grok-2",
                "grok-1.5"
            ]
        }

        return latest_models.get(provider, [])

class ConfigMasterGUI:
    def __init__(self):
        self.config_master = ConfigMaster()
        self.root = None
        self.create_gui()

    def create_gui(self):
        """Tạo giao diện người dùng"""
        self.root = tk.Tk()
        self.root.title("Config Master - Quản lý cấu hình tổng hợp")
        self.root.geometry("900x750")
        self.root.resizable(True, True)

        # Kiểm tra quyền admin
        if not self.config_master.is_admin():
            admin_frame = ttk.Frame(self.root)
            admin_frame.pack(fill=tk.X, padx=10, pady=5)

            ttk.Label(admin_frame, text="⚠️ Cần quyền Administrator để đăng ký Context Menu",
                     foreground="red", font=("Arial", 10, "bold")).pack(side=tk.LEFT)

            ttk.Button(admin_frame, text="Chạy với quyền Admin",
                      command=self.run_as_admin).pack(side=tk.RIGHT)

        # Tạo notebook cho các tab
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Tab 1: Cấu hình AI
        self.create_ai_tab()

        # Tab 2: Phiên bản Python
        self.create_python_tab()

        # Tab 3: Thư viện
        self.create_libraries_tab()

        # Tab 4: Menu chuột phải
        self.create_context_menu_tab()

        # Tab 5: Google Sheet
        self.create_google_sheet_tab()

        # Bottom buttons
        self.create_bottom_buttons()

    def create_ai_tab(self):
        """Tạo tab cấu hình AI"""
        ai_frame = ttk.Frame(self.notebook)
        self.notebook.add(ai_frame, text="Cấu hình AI")

        # AI Provider
        provider_frame = ttk.LabelFrame(ai_frame, text="Nhà cung cấp AI", padding=10)
        provider_frame.pack(fill=tk.X, padx=10, pady=5)

        self.provider_var = tk.StringVar(value=self.config_master.config['ai_provider'])
        providers = list(self.config_master.config['models'].keys())

        provider_names = {"google": "Google (Gemini)", "openai": "OpenAI (GPT)", "grok": "Grok (xAI)"}
        for provider in providers:
            display_name = provider_names.get(provider, provider.title())
            ttk.Radiobutton(provider_frame, text=display_name,
                           variable=self.provider_var, value=provider,
                           command=self.on_provider_change).pack(side=tk.LEFT, padx=15)

        # Model Selection với nút cập nhật
        model_frame = ttk.LabelFrame(ai_frame, text="Lựa chọn Model", padding=10)
        model_frame.pack(fill=tk.X, padx=10, pady=5)

        model_top_frame = ttk.Frame(model_frame)
        model_top_frame.pack(fill=tk.X)

        ttk.Label(model_top_frame, text="Model:").pack(side=tk.LEFT)
        self.model_var = tk.StringVar()
        self.model_combo = ttk.Combobox(model_top_frame, textvariable=self.model_var,
                                       state="readonly", width=35)
        self.model_combo.pack(side=tk.LEFT, padx=10)

        ttk.Button(model_top_frame, text="Cập nhật Model mới nhất",
                  command=self.update_latest_models).pack(side=tk.LEFT, padx=10)

        # API Keys
        api_frame = ttk.LabelFrame(ai_frame, text="Khóa API", padding=10)
        api_frame.pack(fill=tk.X, padx=10, pady=5)

        self.api_entries = {}
        api_names = {"google": "Google AI (Gemini)", "openai": "OpenAI", "grok": "Grok (xAI)"}

        for i, (provider, key) in enumerate(self.config_master.config['api_keys'].items()):
            display_name = api_names.get(provider, provider.title())
            ttk.Label(api_frame, text=f"{display_name} API Key:").grid(row=i, column=0, sticky=tk.W, pady=5)

            entry = ttk.Entry(api_frame, width=50, show="*")
            entry.grid(row=i, column=1, sticky=tk.EW, padx=10, pady=5)
            entry.insert(0, key)
            self.api_entries[provider] = entry

            # Show/Hide button
            show_var = tk.BooleanVar()
            def toggle_show(entry=entry, var=show_var):
                entry.config(show="" if var.get() else "*")
            ttk.Checkbutton(api_frame, text="Hiện", variable=show_var,
                           command=toggle_show).grid(row=i, column=2, padx=5)

        api_frame.columnconfigure(1, weight=1)

        # Test Connection Buttons
        test_frame = ttk.LabelFrame(ai_frame, text="Kiểm tra kết nối", padding=10)
        test_frame.pack(fill=tk.X, padx=10, pady=5)

        test_buttons_frame = ttk.Frame(test_frame)
        test_buttons_frame.pack(fill=tk.X)

        ttk.Button(test_buttons_frame, text="Test Google AI",
                  command=lambda: self.test_ai_connection('google')).pack(side=tk.LEFT, padx=5)
        ttk.Button(test_buttons_frame, text="Test Google Vision",
                  command=lambda: self.test_ai_connection('google_vision')).pack(side=tk.LEFT, padx=5)
        ttk.Button(test_buttons_frame, text="Test OpenAI",
                  command=lambda: self.test_ai_connection('openai')).pack(side=tk.LEFT, padx=5)
        ttk.Button(test_buttons_frame, text="Test Grok",
                  command=lambda: self.test_ai_connection('grok')).pack(side=tk.LEFT, padx=5)
        ttk.Button(test_buttons_frame, text="Test Google Sheet",
                  command=lambda: self.test_ai_connection('google_sheet')).pack(side=tk.LEFT, padx=5)

        # Save AI Config Button
        ttk.Button(test_frame, text="Lưu cấu hình AI",
                  command=self.save_ai_config).pack(pady=10)

        # Update model combo
        self.on_provider_change()

    def create_python_tab(self):
        """Tạo tab quản lý Python versions"""
        python_frame = ttk.Frame(self.notebook)
        self.notebook.add(python_frame, text="Phiên bản Python")

        # Python versions list
        list_frame = ttk.LabelFrame(python_frame, text="Các phiên bản Python đã phát hiện", padding=10)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Treeview for python versions
        columns = ('Tên', 'Đường dẫn', 'Tự động phát hiện')
        self.python_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=8)

        column_widths = {'Tên': 250, 'Đường dẫn': 350, 'Tự động phát hiện': 120}
        for col in columns:
            self.python_tree.heading(col, text=col)
            self.python_tree.column(col, width=column_widths[col])

        # Scrollbar
        python_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.python_tree.yview)
        self.python_tree.configure(yscrollcommand=python_scrollbar.set)

        self.python_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        python_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Buttons
        button_frame = ttk.Frame(python_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(button_frame, text="Làm mới", command=self.refresh_python_versions).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Thêm Python", command=self.add_python_version).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Xóa", command=self.remove_python_version).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Lưu cấu hình Python", command=self.save_python_config).pack(side=tk.RIGHT, padx=5)

        # Selected Python
        selected_frame = ttk.LabelFrame(python_frame, text="Python đã chọn", padding=10)
        selected_frame.pack(fill=tk.X, padx=10, pady=5)

        self.selected_python_var = tk.StringVar()
        self.python_combo = ttk.Combobox(selected_frame, textvariable=self.selected_python_var,
                                        state="readonly", width=50)
        self.python_combo.pack(side=tk.LEFT, padx=10)

        ttk.Button(selected_frame, text="Đặt làm mặc định",
                  command=self.set_default_python).pack(side=tk.LEFT, padx=5)

        # Load python versions
        self.refresh_python_versions()

    def create_libraries_tab(self):
        """Tạo tab quản lý thư viện"""
        lib_frame = ttk.Frame(self.notebook)
        self.notebook.add(lib_frame, text="Thư viện")

        # Python selection for libraries
        python_select_frame = ttk.LabelFrame(lib_frame, text="Python sử dụng để kiểm tra thư viện", padding=10)
        python_select_frame.pack(fill=tk.X, padx=10, pady=5)

        self.lib_python_var = tk.StringVar()
        self.lib_python_combo = ttk.Combobox(python_select_frame, textvariable=self.lib_python_var,
                                            state="readonly", width=50)
        self.lib_python_combo.pack(side=tk.LEFT, padx=10)

        # Status display
        self.lib_status_var = tk.StringVar(value="Chưa kiểm tra thư viện")
        ttk.Label(python_select_frame, textvariable=self.lib_status_var,
                 font=("Arial", 9, "italic")).pack(side=tk.LEFT, padx=20)

        # Status frame
        status_frame = ttk.LabelFrame(lib_frame, text="Trạng thái thư viện", padding=10)
        status_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Installed libraries
        installed_frame = ttk.Frame(status_frame)
        installed_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        ttk.Label(installed_frame, text="Thư viện đã cài đặt:", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        self.installed_text = tk.Text(installed_frame, height=6, state=tk.DISABLED)
        installed_scroll = ttk.Scrollbar(installed_frame, orient=tk.VERTICAL, command=self.installed_text.yview)
        self.installed_text.configure(yscrollcommand=installed_scroll.set)
        self.installed_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        installed_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # Missing libraries
        missing_frame = ttk.Frame(status_frame)
        missing_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        ttk.Label(missing_frame, text="Thư viện thiếu:", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        self.missing_text = tk.Text(missing_frame, height=6, state=tk.DISABLED)
        missing_scroll = ttk.Scrollbar(missing_frame, orient=tk.VERTICAL, command=self.missing_text.yview)
        self.missing_text.configure(yscrollcommand=missing_scroll.set)
        self.missing_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        missing_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # Progress and status
        progress_frame = ttk.Frame(lib_frame)
        progress_frame.pack(fill=tk.X, padx=10, pady=5)

        # Progress bar với label
        self.lib_progress_var = tk.StringVar(value="")
        ttk.Label(progress_frame, textvariable=self.lib_progress_var).pack(anchor=tk.W)

        self.lib_progress = ttk.Progressbar(progress_frame, mode='determinate')
        self.lib_progress.pack(fill=tk.X, pady=5)

        # Buttons
        lib_button_frame = ttk.Frame(lib_frame)
        lib_button_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(lib_button_frame, text="Kiểm tra thư viện",
                  command=self.check_libraries).pack(side=tk.LEFT, padx=5)
        ttk.Button(lib_button_frame, text="Cài đặt thư viện thiếu",
                  command=self.install_libraries).pack(side=tk.LEFT, padx=5)
        ttk.Button(lib_button_frame, text="Lưu cấu hình thư viện",
                  command=self.save_libraries_config).pack(side=tk.RIGHT, padx=5)

        # Update python combo for libraries
        self.update_lib_python_combo()

    def on_provider_change(self):
        """Xử lý khi thay đổi AI provider"""
        provider = self.provider_var.get()
        models = self.config_master.config['models'][provider]['available']
        selected = self.config_master.config['models'][provider]['selected']

        self.model_combo['values'] = models
        self.model_var.set(selected)

    def update_latest_models(self):
        """Cập nhật model mới nhất"""
        provider = self.provider_var.get()
        latest_models = self.config_master.get_latest_models(provider)

        if latest_models:
            self.config_master.config['models'][provider]['available'] = latest_models
            self.model_combo['values'] = latest_models

            # Đặt model đầu tiên làm mặc định nếu model hiện tại không có trong danh sách
            current_model = self.model_var.get()
            if current_model not in latest_models:
                self.model_var.set(latest_models[0])
                self.config_master.config['models'][provider]['selected'] = latest_models[0]

            messagebox.showinfo("Thành công", f"Đã cập nhật {len(latest_models)} model mới nhất cho {provider}")
        else:
            messagebox.showwarning("Cảnh báo", f"Không tìm thấy model cho {provider}")

    def test_ai_connection(self, connection_type):
        """Kiểm tra kết nối AI"""
        # Cập nhật API keys từ giao diện trước khi test
        for provider, entry in self.api_entries.items():
            self.config_master.config['api_keys'][provider] = entry.get()

        if connection_type == 'google':
            status, message = self.config_master.check_google_api_connection()
        elif connection_type == 'google_vision':
            status, message = self.config_master.check_google_vision_connection()
        elif connection_type == 'openai':
            status, message = self.config_master.check_openai_connection()
        elif connection_type == 'grok':
            status, message = self.config_master.check_grok_connection()
        elif connection_type == 'google_sheet':
            status, message = self.config_master.check_google_sheet_connection()
        else:
            status, message = False, "Loại kết nối không hợp lệ"

        if status:
            messagebox.showinfo("Kết nối thành công", message)
        else:
            messagebox.showerror("Kết nối thất bại", message)

    def save_ai_config(self):
        """Lưu cấu hình AI riêng"""
        # Cập nhật config từ giao diện
        self.config_master.config['ai_provider'] = self.provider_var.get()
        self.config_master.config['models'][self.provider_var.get()]['selected'] = self.model_var.get()

        for provider, entry in self.api_entries.items():
            self.config_master.config['api_keys'][provider] = entry.get()

        # Lưu config
        if self.config_master.save_config():
            messagebox.showinfo("Thành công", "Đã lưu cấu hình AI!")
        else:
            messagebox.showerror("Lỗi", "Không thể lưu cấu hình AI!")

    def update_lib_python_combo(self):
        """Cập nhật combo box Python cho thư viện"""
        python_names = list(self.config_master.config['python_versions'].keys())
        self.lib_python_combo['values'] = python_names

        # Chọn Python mặc định
        selected = self.config_master.config.get('selected_python', '')
        if selected in python_names:
            self.lib_python_var.set(selected)
        elif python_names:
            self.lib_python_var.set(python_names[0])

    def refresh_python_versions(self):
        """Refresh danh sách Python versions"""
        # Clear treeview
        for item in self.python_tree.get_children():
            self.python_tree.delete(item)

        # Detect again
        self.config_master.detect_python_versions()

        # Populate treeview
        python_names = []
        for name, info in self.config_master.config['python_versions'].items():
            self.python_tree.insert('', tk.END, values=(
                name, info['path'], 'Có' if info['auto_detected'] else 'Không'
            ))
            python_names.append(name)

        # Update combo
        self.python_combo['values'] = python_names
        self.lib_python_combo['values'] = python_names  # Cập nhật cả combo trong tab thư viện

        selected = self.config_master.config.get('selected_python', '')
        if selected in python_names:
            self.selected_python_var.set(selected)
            self.lib_python_var.set(selected)
        elif python_names:
            self.selected_python_var.set(python_names[0])
            self.lib_python_var.set(python_names[0])

    def add_python_version(self):
        """Thêm Python version mới"""
        file_path = filedialog.askopenfilename(
            title="Chọn Python executable",
            filetypes=[("Executable files", "*.exe"), ("All files", "*.*")]
        )

        if file_path:
            # Kiểm tra xem có phải Python hợp lệ không
            try:
                result = subprocess.run([file_path, '--version'], capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    version_info = result.stdout.strip()
                    name = f"Tùy chỉnh - {version_info}"

                    self.config_master.config['python_versions'][name] = {
                        'path': file_path,
                        'auto_detected': False
                    }
                    self.refresh_python_versions()
                    messagebox.showinfo("Thành công", f"Đã thêm {name}")
                else:
                    messagebox.showerror("Lỗi", "File không phải là Python executable hợp lệ")
            except Exception as e:
                messagebox.showerror("Lỗi", f"Không thể kiểm tra Python: {str(e)}")

    def remove_python_version(self):
        """Xóa Python version"""
        selection = self.python_tree.selection()
        if selection:
            item = self.python_tree.item(selection[0])
            name = item['values'][0]

            if messagebox.askyesno("Xác nhận", f"Xóa {name}?"):
                if name in self.config_master.config['python_versions']:
                    del self.config_master.config['python_versions'][name]
                    self.refresh_python_versions()
                    messagebox.showinfo("Thành công", f"Đã xóa {name}")

    def set_default_python(self):
        """Đặt Python mặc định"""
        selected = self.selected_python_var.get()
        if selected:
            self.config_master.config['selected_python'] = selected
            # Cập nhật cả combo trong tab thư viện
            self.lib_python_var.set(selected)
            messagebox.showinfo("Thành công", f"Đã đặt {selected} làm Python mặc định")

    def save_python_config(self):
        """Lưu cấu hình Python riêng"""
        # Cập nhật selected python
        self.config_master.config['selected_python'] = self.selected_python_var.get()

        if self.config_master.save_config():
            messagebox.showinfo("Thành công", "Đã lưu cấu hình Python!")
        else:
            messagebox.showerror("Lỗi", "Không thể lưu cấu hình Python!")

    def check_libraries(self):
        """Kiểm tra thư viện"""
        python_path = self.get_selected_python_for_lib()
        if not python_path:
            messagebox.showerror("Lỗi", "Chưa chọn phiên bản Python để kiểm tra thư viện")
            return

        def check_thread():
            try:
                # Cập nhật trạng thái
                self.root.after(0, lambda: self.lib_status_var.set(f"Đang kiểm tra thư viện với {self.lib_python_var.get()}..."))
                self.root.after(0, lambda: self.lib_progress_var.set("Đang đọc requirements.txt..."))
                self.root.after(0, lambda: self.lib_progress.config(mode='indeterminate'))
                self.root.after(0, self.lib_progress.start)

                installed, missing = self.config_master.check_libraries(python_path)

                # Update UI in main thread
                self.root.after(0, lambda: self.update_library_display(installed, missing))
                self.root.after(0, lambda: self.lib_status_var.set(f"Đã kiểm tra xong với {self.lib_python_var.get()}"))

            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("Lỗi", f"Lỗi khi kiểm tra thư viện: {str(e)}"))
                self.root.after(0, lambda: self.lib_status_var.set("Lỗi khi kiểm tra thư viện"))
            finally:
                self.root.after(0, self.lib_progress.stop)
                self.root.after(0, lambda: self.lib_progress_var.set(""))

        threading.Thread(target=check_thread, daemon=True).start()

    def get_selected_python_for_lib(self):
        """Lấy Python đã chọn cho thư viện"""
        selected = self.lib_python_var.get()
        if selected and selected in self.config_master.config['python_versions']:
            return self.config_master.config['python_versions'][selected]['path']
        return None

    def update_library_display(self, installed, missing):
        """Cập nhật hiển thị thư viện"""
        # Installed libraries
        self.installed_text.config(state=tk.NORMAL)
        self.installed_text.delete(1.0, tk.END)
        if installed:
            installed_text = f"Đã cài đặt {len(installed)} thư viện:\n" + '\n'.join([f"✅ {lib}" for lib in installed])
        else:
            installed_text = "Chưa có thư viện nào được cài đặt"
        self.installed_text.insert(tk.END, installed_text)
        self.installed_text.config(state=tk.DISABLED)

        # Missing libraries
        self.missing_text.config(state=tk.NORMAL)
        self.missing_text.delete(1.0, tk.END)
        if missing:
            missing_text = f"Thiếu {len(missing)} thư viện:\n" + '\n'.join([f"❌ {lib}" for lib in missing])
        else:
            missing_text = "Tất cả thư viện đã được cài đặt"
        self.missing_text.insert(tk.END, missing_text)
        self.missing_text.config(state=tk.DISABLED)

        # Cập nhật config
        self.config_master.config['libraries']['installed'] = installed
        self.config_master.config['libraries']['missing'] = missing

    def install_libraries(self):
        """Cài đặt thư viện thiếu"""
        python_path = self.get_selected_python_for_lib()
        if not python_path:
            messagebox.showerror("Lỗi", "Chưa chọn phiên bản Python để cài đặt thư viện")
            return

        missing_libs = self.config_master.config.get('libraries', {}).get('missing', [])
        if not missing_libs:
            messagebox.showinfo("Thông báo", "Không có thư viện nào cần cài đặt")
            return

        if not messagebox.askyesno("Xác nhận", f"Cài đặt {len(missing_libs)} thư viện thiếu với {self.lib_python_var.get()}?"):
            return

        def install_thread():
            try:
                # Setup progress bar
                self.root.after(0, lambda: self.lib_progress.config(mode='determinate'))
                self.root.after(0, lambda: self.lib_progress.config(maximum=100))

                # Upgrade pip first
                self.root.after(0, lambda: self.lib_progress_var.set("Đang nâng cấp pip..."))
                self.root.after(0, lambda: self.lib_progress.config(value=10))

                try:
                    subprocess.run([python_path, '-m', 'pip', 'install', '--upgrade', 'pip'],
                                 check=True, capture_output=True, timeout=60)
                except Exception as e:
                    print(f"Warning: Could not upgrade pip: {e}")

                # Install requirements
                self.root.after(0, lambda: self.lib_progress_var.set("Đang cài đặt thư viện từ requirements.txt..."))
                self.root.after(0, lambda: self.lib_progress.config(value=30))

                result = subprocess.run([python_path, '-m', 'pip', 'install', '-r', self.config_master.requirements_file],
                                      capture_output=True, text=True, timeout=300)

                self.root.after(0, lambda: self.lib_progress.config(value=80))

                if result.returncode == 0:
                    # Kiểm tra lại thư viện
                    self.root.after(0, lambda: self.lib_progress_var.set("Đang kiểm tra lại thư viện..."))
                    installed, missing = self.config_master.check_libraries(python_path)

                    self.root.after(0, lambda: self.lib_progress.config(value=100))
                    self.root.after(0, lambda: self.update_library_display(installed, missing))

                    success_msg = f"Cài đặt thành công!\nĐã cài: {len(installed)} thư viện\nCòn thiếu: {len(missing)} thư viện"
                    self.root.after(0, lambda: messagebox.showinfo("Thành công", success_msg))
                    self.root.after(0, lambda: self.lib_status_var.set("Cài đặt thư viện hoàn tất"))
                else:
                    error_msg = f"Lỗi cài đặt:\n{result.stderr[:500]}"
                    self.root.after(0, lambda: messagebox.showerror("Lỗi", error_msg))
                    self.root.after(0, lambda: self.lib_status_var.set("Lỗi cài đặt thư viện"))

            except subprocess.TimeoutExpired:
                self.root.after(0, lambda: messagebox.showerror("Lỗi", "Timeout khi cài đặt thư viện"))
                self.root.after(0, lambda: self.lib_status_var.set("Timeout cài đặt thư viện"))
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("Lỗi", f"Lỗi không mong muốn: {str(e)}"))
                self.root.after(0, lambda: self.lib_status_var.set("Lỗi cài đặt thư viện"))
            finally:
                self.root.after(0, lambda: self.lib_progress_var.set(""))
                self.root.after(0, lambda: self.lib_progress.config(value=0))

        threading.Thread(target=install_thread, daemon=True).start()

    def save_libraries_config(self):
        """Lưu cấu hình thư viện riêng"""
        if self.config_master.save_config():
            messagebox.showinfo("Thành công", "Đã lưu cấu hình thư viện!")
        else:
            messagebox.showerror("Lỗi", "Không thể lưu cấu hình thư viện!")

    def save_context_menu_config(self):
        """Lưu cấu hình context menu riêng"""
        # Cập nhật config từ giao diện
        self.config_master.config['context_menu']['menu_text'] = self.menu_text_var.get()
        self.config_master.config['context_menu']['icon_path'] = self.icon_path_var.get()

        if self.config_master.save_config():
            messagebox.showinfo("Thành công", "Đã lưu cấu hình Menu chuột phải!")
        else:
            messagebox.showerror("Lỗi", "Không thể lưu cấu hình Menu chuột phải!")

    def create_context_menu_tab(self):
        """Tạo tab Context Menu"""
        context_frame = ttk.Frame(self.notebook)
        self.notebook.add(context_frame, text="Menu chuột phải")

        # Status
        status_frame = ttk.LabelFrame(context_frame, text="Trạng thái Menu chuột phải", padding=10)
        status_frame.pack(fill=tk.X, padx=10, pady=5)

        self.context_status_var = tk.StringVar()
        self.context_status_label = ttk.Label(status_frame, textvariable=self.context_status_var,
                                             font=("Arial", 10, "bold"))
        self.context_status_label.pack()

        # Configuration
        config_frame = ttk.LabelFrame(context_frame, text="Cấu hình", padding=10)
        config_frame.pack(fill=tk.X, padx=10, pady=5)

        # Menu text
        ttk.Label(config_frame, text="Tên hiển thị:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.menu_text_var = tk.StringVar(value=self.config_master.config['context_menu']['menu_text'])
        ttk.Entry(config_frame, textvariable=self.menu_text_var, width=50).grid(row=0, column=1, sticky=tk.EW, padx=10, pady=5)

        # Icon path
        ttk.Label(config_frame, text="Đường dẫn Icon:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.icon_path_var = tk.StringVar(value=self.config_master.config['context_menu']['icon_path'])
        icon_entry = ttk.Entry(config_frame, textvariable=self.icon_path_var, width=40)
        icon_entry.grid(row=1, column=1, sticky=tk.EW, padx=10, pady=5)
        ttk.Button(config_frame, text="Chọn file", command=self.browse_icon).grid(row=1, column=2, padx=5)

        config_frame.columnconfigure(1, weight=1)

        # Buttons
        button_frame = ttk.Frame(context_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=20)

        self.register_button = ttk.Button(button_frame, text="Đăng ký Menu chuột phải",
                                         command=self.register_context_menu)
        self.register_button.pack(side=tk.LEFT, padx=5)

        self.unregister_button = ttk.Button(button_frame, text="Hủy đăng ký Menu chuột phải",
                                           command=self.unregister_context_menu)
        self.unregister_button.pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="Lưu cấu hình Menu",
                  command=self.save_context_menu_config).pack(side=tk.RIGHT, padx=5)

        # Update status
        self.update_context_menu_status()

    def create_google_sheet_tab(self):
        """Tạo tab Google Sheet"""
        sheet_frame = ttk.Frame(self.notebook)
        self.notebook.add(sheet_frame, text="Google Sheet")

        # Google Sheet Configuration
        config_frame = ttk.LabelFrame(sheet_frame, text="Cấu hình Google Sheet", padding=10)
        config_frame.pack(fill=tk.X, padx=10, pady=5)

        # Sheet URL
        ttk.Label(config_frame, text="URL Google Sheet:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.sheet_url_var = tk.StringVar(value=self.config_master.config['google_sheet']['url'])
        ttk.Entry(config_frame, textvariable=self.sheet_url_var, width=60).grid(row=0, column=1, sticky=tk.EW, padx=10, pady=5)

        # Sheet Name
        ttk.Label(config_frame, text="Tên Sheet:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.sheet_name_var = tk.StringVar(value=self.config_master.config['google_sheet']['name'])
        sheet_name_entry = ttk.Entry(config_frame, textvariable=self.sheet_name_var, width=60, state="readonly")
        sheet_name_entry.grid(row=1, column=1, sticky=tk.EW, padx=10, pady=5)

        # Credentials file
        ttk.Label(config_frame, text="File Credentials:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.credentials_var = tk.StringVar(value=self.config_master.config['google_sheet']['credentials_file'])
        cred_entry = ttk.Entry(config_frame, textvariable=self.credentials_var, width=50)
        cred_entry.grid(row=2, column=1, sticky=tk.EW, padx=10, pady=5)
        ttk.Button(config_frame, text="Chọn file", command=self.browse_credentials).grid(row=2, column=2, padx=5)

        config_frame.columnconfigure(1, weight=1)

        # Test connection and save
        button_frame = ttk.Frame(sheet_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(button_frame, text="Kiểm tra kết nối", command=self.test_google_sheet).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Lưu cấu hình Google Sheet", command=self.save_google_sheet_config).pack(side=tk.RIGHT, padx=5)

    def create_bottom_buttons(self):
        """Tạo các nút ở dưới"""
        bottom_frame = ttk.Frame(self.root)
        bottom_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(bottom_frame, text="Lưu tất cả cấu hình",
                  command=self.save_configuration).pack(side=tk.LEFT, padx=5)
        ttk.Button(bottom_frame, text="Xuất cấu hình cho rename_pdf.py",
                  command=self.export_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(bottom_frame, text="Thoát", command=self.root.quit).pack(side=tk.RIGHT, padx=5)

    def browse_icon(self):
        """Chọn file icon"""
        file_path = filedialog.askopenfilename(
            title="Chọn file icon",
            filetypes=[("Icon files", "*.ico"), ("Image files", "*.png;*.jpg;*.bmp"), ("All files", "*.*")]
        )
        if file_path:
            self.icon_path_var.set(file_path)

    def browse_credentials(self):
        """Chọn file credentials"""
        file_path = filedialog.askopenfilename(
            title="Chọn Google credentials file",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if file_path:
            self.credentials_var.set(file_path)

    def save_google_sheet_config(self):
        """Lưu cấu hình Google Sheet riêng"""
        # Cập nhật config từ giao diện
        self.config_master.config['google_sheet']['url'] = self.sheet_url_var.get()
        self.config_master.config['google_sheet']['name'] = self.sheet_name_var.get()
        self.config_master.config['google_sheet']['credentials_file'] = self.credentials_var.get()

        if self.config_master.save_config():
            messagebox.showinfo("Thành công", "Đã lưu cấu hình Google Sheet!")
        else:
            messagebox.showerror("Lỗi", "Không thể lưu cấu hình Google Sheet!")

    def test_google_sheet(self):
        """Test kết nối Google Sheet"""
        # Cập nhật config trước khi test
        self.config_master.config['google_sheet']['url'] = self.sheet_url_var.get()
        self.config_master.config['google_sheet']['credentials_file'] = self.credentials_var.get()

        status, message = self.config_master.check_google_sheet_connection()
        if status:
            # Cập nhật tên sheet nếu thành công
            self.sheet_name_var.set(self.config_master.config['google_sheet']['name'])
            messagebox.showinfo("Kết nối thành công", message)
        else:
            messagebox.showerror("Kết nối thất bại", message)

    def update_context_menu_status(self):
        """Cập nhật trạng thái context menu"""
        try:
            # Kiểm tra registry
            winreg.OpenKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\RenamePDF")
            self.context_status_var.set("✅ Menu chuột phải đã được đăng ký")
            self.config_master.config['context_menu']['enabled'] = True
        except:
            self.context_status_var.set("❌ Menu chuột phải chưa được đăng ký")
            self.config_master.config['context_menu']['enabled'] = False

    def register_context_menu(self):
        """Đăng ký context menu"""
        if not self.config_master.is_admin():
            messagebox.showerror("Lỗi", "Cần quyền Administrator để đăng ký Menu chuột phải")
            return

        # Update config from UI
        self.config_master.config['context_menu']['menu_text'] = self.menu_text_var.get()
        self.config_master.config['context_menu']['icon_path'] = self.icon_path_var.get()

        success, message = self.config_master.register_context_menu()
        if success:
            messagebox.showinfo("Thành công", message)
        else:
            messagebox.showerror("Lỗi", message)

        self.update_context_menu_status()

    def unregister_context_menu(self):
        """Hủy đăng ký context menu"""
        if not self.config_master.is_admin():
            messagebox.showerror("Lỗi", "Cần quyền Administrator để hủy đăng ký Menu chuột phải")
            return

        success, message = self.config_master.unregister_context_menu()
        if success:
            messagebox.showinfo("Thành công", message)
        else:
            messagebox.showerror("Lỗi", message)

        self.update_context_menu_status()

    def run_as_admin(self):
        """Chạy với quyền admin"""
        if self.config_master.run_as_admin():
            self.root.quit()
        else:
            messagebox.showerror("Lỗi", "Không thể chạy với quyền Administrator")

    def save_configuration(self):
        """Lưu tất cả cấu hình"""
        try:
            # Update config from UI
            self.config_master.config['ai_provider'] = self.provider_var.get()
            self.config_master.config['models'][self.provider_var.get()]['selected'] = self.model_var.get()

            # API Keys
            for provider, entry in self.api_entries.items():
                self.config_master.config['api_keys'][provider] = entry.get()

            # Python
            self.config_master.config['selected_python'] = self.selected_python_var.get()

            # Context Menu
            self.config_master.config['context_menu']['menu_text'] = self.menu_text_var.get()
            self.config_master.config['context_menu']['icon_path'] = self.icon_path_var.get()

            # Google Sheet
            self.config_master.config['google_sheet']['url'] = self.sheet_url_var.get()
            self.config_master.config['google_sheet']['name'] = self.sheet_name_var.get()
            self.config_master.config['google_sheet']['credentials_file'] = self.credentials_var.get()

            if self.config_master.save_config():
                messagebox.showinfo("Thành công", "Đã lưu tất cả cấu hình thành công!")
            else:
                messagebox.showerror("Lỗi", "Lỗi khi lưu cấu hình!")
        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi không mong muốn khi lưu cấu hình: {str(e)}")

    def export_config(self):
        """Export cấu hình cho rename_pdf.py"""
        try:
            # Cập nhật config trước khi export
            self.save_configuration()

            # Tạo config đơn giản cho rename_pdf.py
            export_config = {
                "ai_provider": self.config_master.config['ai_provider'],
                "selected_model": self.config_master.config['models'][self.config_master.config['ai_provider']]['selected'],
                "api_keys": self.config_master.config['api_keys'],
                "google_sheet": self.config_master.config['google_sheet'],
                "python_path": self.config_master.get_selected_python_path(),
                "processing_settings": {
                    "max_retries": 3,
                    "timeout_seconds": 300,
                    "image_quality": "high",
                    "ocr_language": "vie+eng"
                },
                "file_settings": {
                    "max_filename_length": 200,
                    "backup_original": True,
                    "output_format": "pdf"
                },
                "ui_settings": {
                    "show_progress": True,
                    "auto_close_on_success": False,
                    "log_level": "INFO"
                }
            }

            export_file = os.path.join(self.config_master.script_dir, 'rename_pdf_config.json')
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(export_config, f, indent=4, ensure_ascii=False)
            messagebox.showinfo("Thành công", f"Đã xuất cấu hình vào {export_file}")
        except Exception as e:
            messagebox.showerror("Lỗi", f"Lỗi khi xuất cấu hình: {str(e)}")

    def run(self):
        """Chạy GUI"""
        self.root.mainloop()

def main():
    """Hàm main"""
    try:
        app = ConfigMasterGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("Error", f"Lỗi khởi tạo: {str(e)}")

if __name__ == "__main__":
    main()
