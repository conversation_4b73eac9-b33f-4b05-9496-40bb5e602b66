#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Config Master - <PERSON><PERSON><PERSON><PERSON> lý c<PERSON>u hình tổng hợp
<PERSON><PERSON> chức năng từ config.py, config_manager.py, python_version_manager.py, register_pdf_context_menu.py
"""

import os
import sys
import json
import subprocess
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import winreg
import threading
import time
from pathlib import Path
import ctypes

class ConfigMaster:
    def __init__(self):
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.config_file = os.path.join(self.script_dir, 'master_config.json')
        self.requirements_file = os.path.join(self.script_dir, 'requirements.txt')
        
        # Load default config
        self.default_config = {
            "ai_provider": "google",
            "models": {
                "google": {
                    "selected": "gemini-1.5-flash",
                    "available": [
                        "gemini-1.5-flash",
                        "gemini-1.5-pro",
                        "gemini-pro",
                        "gemini-pro-vision"
                    ]
                },
                "openai": {
                    "selected": "gpt-4o-mini",
                    "available": [
                        "gpt-4o",
                        "gpt-4o-mini",
                        "gpt-4-turbo",
                        "gpt-4",
                        "gpt-3.5-turbo"
                    ]
                },
                "grok": {
                    "selected": "grok-beta",
                    "available": [
                        "grok-beta",
                        "grok-vision-beta",
                        "grok-2",
                        "grok-1.5"
                    ]
                }
            },
            "api_keys": {
                "google": "",
                "openai": "",
                "grok": ""
            },
            "google_sheet": {
                "url": "",
                "name": "",
                "credentials_file": ""
            },
            "python_versions": {},
            "selected_python": "",
            "context_menu": {
                "enabled": False,
                "menu_text": "Đổi tên PDF và lưu Google Sheet",
                "icon_path": "",
                "use_vbs": False
            },
            "libraries": {
                "installed": [],
                "missing": []
            }
        }
        
        self.config = self.default_config.copy()
        self.load_config()
        self.detect_python_versions()
        
    def load_config(self):
        """Load cấu hình từ file"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    self.update_config_recursive(self.config, loaded_config)
            except Exception as e:
                print(f"Lỗi khi đọc config: {e}")
        
    def update_config_recursive(self, target, source):
        """Cập nhật config một cách đệ quy"""
        for key, value in source.items():
            if key in target:
                if isinstance(value, dict) and isinstance(target[key], dict):
                    self.update_config_recursive(target[key], value)
                else:
                    target[key] = value
            else:
                target[key] = value
                
    def save_config(self):
        """Lưu cấu hình vào file"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"Lỗi khi lưu config: {e}")
            return False
            
    def detect_python_versions(self):
        """Tự động phát hiện các phiên bản Python"""
        python_versions = {}
        
        # Kiểm tra Python trong PATH
        try:
            result = subprocess.run(['python', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                version = result.stdout.strip()
                python_versions[f"{version} (PATH)"] = {
                    'path': 'python',
                    'auto_detected': True
                }
        except:
            pass
            
        # Kiểm tra các đường dẫn phổ biến
        common_paths = [
            r"C:\Python*\python.exe",
            r"C:\Users\<USER>\AppData\Local\Programs\Python\Python*\python.exe",
            r"C:\Program Files\Python*\python.exe",
            r"C:\Program Files (x86)\Python*\python.exe"
        ]
        
        import glob
        for pattern in common_paths:
            for path in glob.glob(pattern):
                if os.path.exists(path):
                    try:
                        result = subprocess.run([path, '--version'], capture_output=True, text=True)
                        if result.returncode == 0:
                            version = result.stdout.strip()
                            name = f"{version} ({os.path.dirname(path)})"
                            python_versions[name] = {
                                'path': path,
                                'auto_detected': True
                            }
                    except:
                        continue
        
        # Cập nhật config
        if 'python_versions' not in self.config:
            self.config['python_versions'] = {}
            
        # Thêm các phiên bản mới phát hiện
        for name, info in python_versions.items():
            if name not in self.config['python_versions']:
                self.config['python_versions'][name] = info
                
    def check_libraries(self, python_path=None):
        """Kiểm tra thư viện đã cài đặt"""
        if not python_path:
            python_path = self.get_selected_python_path()
            
        if not python_path:
            return [], []
            
        # Đọc requirements.txt
        required_libs = []
        if os.path.exists(self.requirements_file):
            with open(self.requirements_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        # Lấy tên package (bỏ version)
                        lib_name = line.split('>=')[0].split('==')[0].split('<')[0].split('>')[0]
                        required_libs.append(lib_name.strip())
        
        # Kiểm tra thư viện đã cài đặt
        installed = []
        missing = []
        
        for lib in required_libs:
            try:
                result = subprocess.run([python_path, '-c', f'import {lib}'], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    installed.append(lib)
                else:
                    missing.append(lib)
            except:
                missing.append(lib)
                
        self.config['libraries']['installed'] = installed
        self.config['libraries']['missing'] = missing
        
        return installed, missing
        
    def install_libraries(self, python_path=None, progress_callback=None):
        """Cài đặt thư viện thiếu"""
        if not python_path:
            python_path = self.get_selected_python_path()
            
        if not python_path or not os.path.exists(self.requirements_file):
            return False, "Không tìm thấy Python hoặc requirements.txt"
            
        try:
            # Upgrade pip trước
            if progress_callback:
                progress_callback("Đang nâng cấp pip...")
                
            subprocess.run([python_path, '-m', 'pip', 'install', '--upgrade', 'pip'], 
                         check=True, capture_output=True)
            
            # Cài đặt requirements
            if progress_callback:
                progress_callback("Đang cài đặt thư viện...")
                
            result = subprocess.run([python_path, '-m', 'pip', 'install', '-r', self.requirements_file], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                # Cập nhật lại danh sách thư viện
                self.check_libraries(python_path)
                return True, "Cài đặt thành công!"
            else:
                return False, f"Lỗi cài đặt: {result.stderr}"
                
        except Exception as e:
            return False, f"Lỗi: {str(e)}"
            
    def get_selected_python_path(self):
        """Lấy đường dẫn Python đã chọn"""
        selected = self.config.get('selected_python', '')
        if selected and selected in self.config['python_versions']:
            return self.config['python_versions'][selected]['path']
        return None
        
    def register_context_menu(self):
        """Đăng ký context menu cho PDF"""
        try:
            python_path = self.get_selected_python_path()
            if not python_path:
                return False, "Chưa chọn phiên bản Python"
                
            rename_script = os.path.join(self.script_dir, 'rename_pdf.py')
            if not os.path.exists(rename_script):
                return False, "Không tìm thấy rename_pdf.py"
            
            # Tạo key cho PDF files
            pdf_key_path = r"*\shell\RenamePDF"
            
            # Tạo key chính
            with winreg.CreateKey(winreg.HKEY_CLASSES_ROOT, pdf_key_path) as key:
                winreg.SetValue(key, "", winreg.REG_SZ, self.config['context_menu']['menu_text'])
                
                # Thêm icon nếu có
                icon_path = self.config['context_menu']['icon_path']
                if icon_path and os.path.exists(icon_path):
                    winreg.SetValueEx(key, "Icon", 0, winreg.REG_SZ, icon_path)
            
            # Tạo command key
            command_key_path = pdf_key_path + r"\command"
            with winreg.CreateKey(winreg.HKEY_CLASSES_ROOT, command_key_path) as key:
                # Sử dụng pythonw.exe để ẩn terminal
                pythonw_path = python_path.replace('python.exe', 'pythonw.exe')
                if not os.path.exists(pythonw_path):
                    pythonw_path = python_path
                    
                command = f'"{pythonw_path}" "{rename_script}" "%1"'
                winreg.SetValue(key, "", winreg.REG_SZ, command)
            
            self.config['context_menu']['enabled'] = True
            return True, "Đã đăng ký context menu thành công!"
            
        except Exception as e:
            return False, f"Lỗi khi đăng ký: {str(e)}"
            
    def unregister_context_menu(self):
        """Hủy đăng ký context menu"""
        try:
            pdf_key_path = r"*\shell\RenamePDF"
            winreg.DeleteKey(winreg.HKEY_CLASSES_ROOT, pdf_key_path + r"\command")
            winreg.DeleteKey(winreg.HKEY_CLASSES_ROOT, pdf_key_path)
            
            self.config['context_menu']['enabled'] = False
            return True, "Đã hủy đăng ký context menu thành công!"
            
        except Exception as e:
            return False, f"Lỗi khi hủy đăng ký: {str(e)}"
            
    def is_admin(self):
        """Kiểm tra quyền admin"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
            
    def run_as_admin(self):
        """Chạy lại với quyền admin"""
        if self.is_admin():
            return True

        try:
            ctypes.windll.shell32.ShellExecuteW(
                None, "runas", sys.executable, " ".join(sys.argv), None, 1
            )
            return True
        except:
            return False

class ConfigMasterGUI:
    def __init__(self):
        self.config_master = ConfigMaster()
        self.root = None
        self.create_gui()

    def create_gui(self):
        """Tạo giao diện người dùng"""
        self.root = tk.Tk()
        self.root.title("Config Master - Quản lý cấu hình tổng hợp")
        self.root.geometry("800x700")
        self.root.resizable(True, True)

        # Kiểm tra quyền admin
        if not self.config_master.is_admin():
            admin_frame = ttk.Frame(self.root)
            admin_frame.pack(fill=tk.X, padx=10, pady=5)

            ttk.Label(admin_frame, text="⚠️ Cần quyền Administrator để đăng ký Context Menu",
                     foreground="red", font=("Arial", 10, "bold")).pack(side=tk.LEFT)

            ttk.Button(admin_frame, text="Chạy với quyền Admin",
                      command=self.run_as_admin).pack(side=tk.RIGHT)

        # Tạo notebook cho các tab
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Tab 1: AI Configuration
        self.create_ai_tab()

        # Tab 2: Python Versions
        self.create_python_tab()

        # Tab 3: Libraries
        self.create_libraries_tab()

        # Tab 4: Context Menu
        self.create_context_menu_tab()

        # Tab 5: Google Sheet
        self.create_google_sheet_tab()

        # Bottom buttons
        self.create_bottom_buttons()

    def create_ai_tab(self):
        """Tạo tab cấu hình AI"""
        ai_frame = ttk.Frame(self.notebook)
        self.notebook.add(ai_frame, text="AI Configuration")

        # AI Provider
        provider_frame = ttk.LabelFrame(ai_frame, text="AI Provider", padding=10)
        provider_frame.pack(fill=tk.X, padx=10, pady=5)

        self.provider_var = tk.StringVar(value=self.config_master.config['ai_provider'])
        providers = list(self.config_master.config['models'].keys())

        for provider in providers:
            ttk.Radiobutton(provider_frame, text=provider.title(),
                           variable=self.provider_var, value=provider,
                           command=self.on_provider_change).pack(side=tk.LEFT, padx=10)

        # Model Selection
        model_frame = ttk.LabelFrame(ai_frame, text="Model Selection", padding=10)
        model_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(model_frame, text="Model:").pack(side=tk.LEFT)
        self.model_var = tk.StringVar()
        self.model_combo = ttk.Combobox(model_frame, textvariable=self.model_var,
                                       state="readonly", width=30)
        self.model_combo.pack(side=tk.LEFT, padx=10)

        # API Keys
        api_frame = ttk.LabelFrame(ai_frame, text="API Keys", padding=10)
        api_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        self.api_entries = {}
        for i, (provider, key) in enumerate(self.config_master.config['api_keys'].items()):
            ttk.Label(api_frame, text=f"{provider.title()} API Key:").grid(row=i, column=0, sticky=tk.W, pady=5)
            entry = ttk.Entry(api_frame, width=50, show="*")
            entry.grid(row=i, column=1, sticky=tk.EW, padx=10, pady=5)
            entry.insert(0, key)
            self.api_entries[provider] = entry

            # Show/Hide button
            show_var = tk.BooleanVar()
            def toggle_show(entry=entry, var=show_var):
                entry.config(show="" if var.get() else "*")
            ttk.Checkbutton(api_frame, text="Show", variable=show_var,
                           command=toggle_show).grid(row=i, column=2, padx=5)

        api_frame.columnconfigure(1, weight=1)

        # Update model combo
        self.on_provider_change()

    def create_python_tab(self):
        """Tạo tab quản lý Python versions"""
        python_frame = ttk.Frame(self.notebook)
        self.notebook.add(python_frame, text="Python Versions")

        # Python versions list
        list_frame = ttk.LabelFrame(python_frame, text="Detected Python Versions", padding=10)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Treeview for python versions
        columns = ('Name', 'Path', 'Auto Detected')
        self.python_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=8)

        for col in columns:
            self.python_tree.heading(col, text=col)
            self.python_tree.column(col, width=200)

        # Scrollbar
        python_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.python_tree.yview)
        self.python_tree.configure(yscrollcommand=python_scrollbar.set)

        self.python_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        python_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Buttons
        button_frame = ttk.Frame(python_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(button_frame, text="Refresh", command=self.refresh_python_versions).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Add Python", command=self.add_python_version).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Remove", command=self.remove_python_version).pack(side=tk.LEFT, padx=5)

        # Selected Python
        selected_frame = ttk.LabelFrame(python_frame, text="Selected Python", padding=10)
        selected_frame.pack(fill=tk.X, padx=10, pady=5)

        self.selected_python_var = tk.StringVar()
        self.python_combo = ttk.Combobox(selected_frame, textvariable=self.selected_python_var,
                                        state="readonly", width=50)
        self.python_combo.pack(side=tk.LEFT, padx=10)

        ttk.Button(selected_frame, text="Set as Default",
                  command=self.set_default_python).pack(side=tk.LEFT, padx=5)

        # Load python versions
        self.refresh_python_versions()

    def create_libraries_tab(self):
        """Tạo tab quản lý thư viện"""
        lib_frame = ttk.Frame(self.notebook)
        self.notebook.add(lib_frame, text="Libraries")

        # Status frame
        status_frame = ttk.LabelFrame(lib_frame, text="Library Status", padding=10)
        status_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Installed libraries
        installed_frame = ttk.Frame(status_frame)
        installed_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        ttk.Label(installed_frame, text="Installed Libraries:", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        self.installed_text = tk.Text(installed_frame, height=8, state=tk.DISABLED)
        installed_scroll = ttk.Scrollbar(installed_frame, orient=tk.VERTICAL, command=self.installed_text.yview)
        self.installed_text.configure(yscrollcommand=installed_scroll.set)
        self.installed_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        installed_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # Missing libraries
        missing_frame = ttk.Frame(status_frame)
        missing_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        ttk.Label(missing_frame, text="Missing Libraries:", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        self.missing_text = tk.Text(missing_frame, height=8, state=tk.DISABLED)
        missing_scroll = ttk.Scrollbar(missing_frame, orient=tk.VERTICAL, command=self.missing_text.yview)
        self.missing_text.configure(yscrollcommand=missing_scroll.set)
        self.missing_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        missing_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # Buttons
        lib_button_frame = ttk.Frame(lib_frame)
        lib_button_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(lib_button_frame, text="Check Libraries",
                  command=self.check_libraries).pack(side=tk.LEFT, padx=5)
        ttk.Button(lib_button_frame, text="Install Missing",
                  command=self.install_libraries).pack(side=tk.LEFT, padx=5)

        # Progress bar
        self.lib_progress = ttk.Progressbar(lib_button_frame, mode='indeterminate')
        self.lib_progress.pack(side=tk.RIGHT, padx=5)

        # Initial check
        self.check_libraries()

    def on_provider_change(self):
        """Xử lý khi thay đổi AI provider"""
        provider = self.provider_var.get()
        models = self.config_master.config['models'][provider]['available']
        selected = self.config_master.config['models'][provider]['selected']

        self.model_combo['values'] = models
        self.model_var.set(selected)

    def refresh_python_versions(self):
        """Refresh danh sách Python versions"""
        # Clear treeview
        for item in self.python_tree.get_children():
            self.python_tree.delete(item)

        # Detect again
        self.config_master.detect_python_versions()

        # Populate treeview
        python_names = []
        for name, info in self.config_master.config['python_versions'].items():
            self.python_tree.insert('', tk.END, values=(
                name, info['path'], 'Yes' if info['auto_detected'] else 'No'
            ))
            python_names.append(name)

        # Update combo
        self.python_combo['values'] = python_names
        selected = self.config_master.config.get('selected_python', '')
        if selected in python_names:
            self.selected_python_var.set(selected)
        elif python_names:
            self.selected_python_var.set(python_names[0])

    def add_python_version(self):
        """Thêm Python version mới"""
        file_path = filedialog.askopenfilename(
            title="Chọn Python executable",
            filetypes=[("Executable files", "*.exe"), ("All files", "*.*")]
        )

        if file_path:
            name = f"Custom - {os.path.basename(os.path.dirname(file_path))}"
            self.config_master.config['python_versions'][name] = {
                'path': file_path,
                'auto_detected': False
            }
            self.refresh_python_versions()

    def remove_python_version(self):
        """Xóa Python version"""
        selection = self.python_tree.selection()
        if selection:
            item = self.python_tree.item(selection[0])
            name = item['values'][0]

            if messagebox.askyesno("Confirm", f"Remove {name}?"):
                if name in self.config_master.config['python_versions']:
                    del self.config_master.config['python_versions'][name]
                    self.refresh_python_versions()

    def set_default_python(self):
        """Đặt Python mặc định"""
        selected = self.selected_python_var.get()
        if selected:
            self.config_master.config['selected_python'] = selected
            messagebox.showinfo("Success", f"Set {selected} as default Python")

    def check_libraries(self):
        """Kiểm tra thư viện"""
        def check_thread():
            self.lib_progress.start()
            try:
                installed, missing = self.config_master.check_libraries()

                # Update UI in main thread
                self.root.after(0, lambda: self.update_library_display(installed, missing))
            finally:
                self.root.after(0, self.lib_progress.stop)

        threading.Thread(target=check_thread, daemon=True).start()

    def update_library_display(self, installed, missing):
        """Cập nhật hiển thị thư viện"""
        # Installed libraries
        self.installed_text.config(state=tk.NORMAL)
        self.installed_text.delete(1.0, tk.END)
        self.installed_text.insert(tk.END, '\n'.join(installed))
        self.installed_text.config(state=tk.DISABLED)

        # Missing libraries
        self.missing_text.config(state=tk.NORMAL)
        self.missing_text.delete(1.0, tk.END)
        self.missing_text.insert(tk.END, '\n'.join(missing))
        self.missing_text.config(state=tk.DISABLED)

    def install_libraries(self):
        """Cài đặt thư viện thiếu"""
        if not self.config_master.get_selected_python_path():
            messagebox.showerror("Error", "Chưa chọn phiên bản Python")
            return

        def install_thread():
            self.lib_progress.start()
            try:
                def progress_callback(msg):
                    self.root.after(0, lambda: self.update_status(msg))

                success, message = self.config_master.install_libraries(progress_callback=progress_callback)
                self.root.after(0, lambda: self.show_install_result(success, message))
            finally:
                self.root.after(0, self.lib_progress.stop)

        threading.Thread(target=install_thread, daemon=True).start()

    def update_status(self, message):
        """Cập nhật trạng thái"""
        # Có thể thêm status bar sau
        print(message)

    def show_install_result(self, success, message):
        """Hiển thị kết quả cài đặt"""
        if success:
            messagebox.showinfo("Success", message)
            self.check_libraries()  # Refresh library status
        else:
            messagebox.showerror("Error", message)

    def create_context_menu_tab(self):
        """Tạo tab Context Menu"""
        context_frame = ttk.Frame(self.notebook)
        self.notebook.add(context_frame, text="Context Menu")

        # Status
        status_frame = ttk.LabelFrame(context_frame, text="Context Menu Status", padding=10)
        status_frame.pack(fill=tk.X, padx=10, pady=5)

        self.context_status_var = tk.StringVar()
        self.context_status_label = ttk.Label(status_frame, textvariable=self.context_status_var,
                                             font=("Arial", 10, "bold"))
        self.context_status_label.pack()

        # Configuration
        config_frame = ttk.LabelFrame(context_frame, text="Configuration", padding=10)
        config_frame.pack(fill=tk.X, padx=10, pady=5)

        # Menu text
        ttk.Label(config_frame, text="Menu Text:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.menu_text_var = tk.StringVar(value=self.config_master.config['context_menu']['menu_text'])
        ttk.Entry(config_frame, textvariable=self.menu_text_var, width=50).grid(row=0, column=1, sticky=tk.EW, padx=10, pady=5)

        # Icon path
        ttk.Label(config_frame, text="Icon Path:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.icon_path_var = tk.StringVar(value=self.config_master.config['context_menu']['icon_path'])
        icon_entry = ttk.Entry(config_frame, textvariable=self.icon_path_var, width=40)
        icon_entry.grid(row=1, column=1, sticky=tk.EW, padx=10, pady=5)
        ttk.Button(config_frame, text="Browse", command=self.browse_icon).grid(row=1, column=2, padx=5)

        config_frame.columnconfigure(1, weight=1)

        # Buttons
        button_frame = ttk.Frame(context_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=20)

        self.register_button = ttk.Button(button_frame, text="Register Context Menu",
                                         command=self.register_context_menu)
        self.register_button.pack(side=tk.LEFT, padx=5)

        self.unregister_button = ttk.Button(button_frame, text="Unregister Context Menu",
                                           command=self.unregister_context_menu)
        self.unregister_button.pack(side=tk.LEFT, padx=5)

        # Update status
        self.update_context_menu_status()

    def create_google_sheet_tab(self):
        """Tạo tab Google Sheet"""
        sheet_frame = ttk.Frame(self.notebook)
        self.notebook.add(sheet_frame, text="Google Sheet")

        # Google Sheet Configuration
        config_frame = ttk.LabelFrame(sheet_frame, text="Google Sheet Configuration", padding=10)
        config_frame.pack(fill=tk.X, padx=10, pady=5)

        # Sheet URL
        ttk.Label(config_frame, text="Sheet URL:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.sheet_url_var = tk.StringVar(value=self.config_master.config['google_sheet']['url'])
        ttk.Entry(config_frame, textvariable=self.sheet_url_var, width=60).grid(row=0, column=1, sticky=tk.EW, padx=10, pady=5)

        # Sheet Name
        ttk.Label(config_frame, text="Sheet Name:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.sheet_name_var = tk.StringVar(value=self.config_master.config['google_sheet']['name'])
        ttk.Entry(config_frame, textvariable=self.sheet_name_var, width=60).grid(row=1, column=1, sticky=tk.EW, padx=10, pady=5)

        # Credentials file
        ttk.Label(config_frame, text="Credentials File:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.credentials_var = tk.StringVar(value=self.config_master.config['google_sheet']['credentials_file'])
        cred_entry = ttk.Entry(config_frame, textvariable=self.credentials_var, width=50)
        cred_entry.grid(row=2, column=1, sticky=tk.EW, padx=10, pady=5)
        ttk.Button(config_frame, text="Browse", command=self.browse_credentials).grid(row=2, column=2, padx=5)

        config_frame.columnconfigure(1, weight=1)

        # Test connection
        test_frame = ttk.Frame(sheet_frame)
        test_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(test_frame, text="Test Connection", command=self.test_google_sheet).pack(side=tk.LEFT, padx=5)

    def create_bottom_buttons(self):
        """Tạo các nút ở dưới"""
        bottom_frame = ttk.Frame(self.root)
        bottom_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(bottom_frame, text="Save Configuration",
                  command=self.save_configuration).pack(side=tk.LEFT, padx=5)
        ttk.Button(bottom_frame, text="Export for rename_pdf.py",
                  command=self.export_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(bottom_frame, text="Exit", command=self.root.quit).pack(side=tk.RIGHT, padx=5)

    def browse_icon(self):
        """Chọn file icon"""
        file_path = filedialog.askopenfilename(
            title="Chọn file icon",
            filetypes=[("Icon files", "*.ico"), ("Image files", "*.png;*.jpg;*.bmp"), ("All files", "*.*")]
        )
        if file_path:
            self.icon_path_var.set(file_path)

    def browse_credentials(self):
        """Chọn file credentials"""
        file_path = filedialog.askopenfilename(
            title="Chọn Google credentials file",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if file_path:
            self.credentials_var.set(file_path)

    def update_context_menu_status(self):
        """Cập nhật trạng thái context menu"""
        try:
            # Kiểm tra registry
            winreg.OpenKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\RenamePDF")
            self.context_status_var.set("✅ Context Menu đã được đăng ký")
            self.config_master.config['context_menu']['enabled'] = True
        except:
            self.context_status_var.set("❌ Context Menu chưa được đăng ký")
            self.config_master.config['context_menu']['enabled'] = False

    def register_context_menu(self):
        """Đăng ký context menu"""
        if not self.config_master.is_admin():
            messagebox.showerror("Error", "Cần quyền Administrator để đăng ký Context Menu")
            return

        # Update config from UI
        self.config_master.config['context_menu']['menu_text'] = self.menu_text_var.get()
        self.config_master.config['context_menu']['icon_path'] = self.icon_path_var.get()

        success, message = self.config_master.register_context_menu()
        if success:
            messagebox.showinfo("Success", message)
        else:
            messagebox.showerror("Error", message)

        self.update_context_menu_status()

    def unregister_context_menu(self):
        """Hủy đăng ký context menu"""
        if not self.config_master.is_admin():
            messagebox.showerror("Error", "Cần quyền Administrator để hủy đăng ký Context Menu")
            return

        success, message = self.config_master.unregister_context_menu()
        if success:
            messagebox.showinfo("Success", message)
        else:
            messagebox.showerror("Error", message)

        self.update_context_menu_status()

    def test_google_sheet(self):
        """Test kết nối Google Sheet"""
        # Placeholder - có thể implement sau
        messagebox.showinfo("Info", "Chức năng test Google Sheet sẽ được implement sau")

    def run_as_admin(self):
        """Chạy với quyền admin"""
        if self.config_master.run_as_admin():
            self.root.quit()
        else:
            messagebox.showerror("Error", "Không thể chạy với quyền Administrator")

    def save_configuration(self):
        """Lưu cấu hình"""
        # Update config from UI
        self.config_master.config['ai_provider'] = self.provider_var.get()
        self.config_master.config['models'][self.provider_var.get()]['selected'] = self.model_var.get()

        # API Keys
        for provider, entry in self.api_entries.items():
            self.config_master.config['api_keys'][provider] = entry.get()

        # Python
        self.config_master.config['selected_python'] = self.selected_python_var.get()

        # Context Menu
        self.config_master.config['context_menu']['menu_text'] = self.menu_text_var.get()
        self.config_master.config['context_menu']['icon_path'] = self.icon_path_var.get()

        # Google Sheet
        self.config_master.config['google_sheet']['url'] = self.sheet_url_var.get()
        self.config_master.config['google_sheet']['name'] = self.sheet_name_var.get()
        self.config_master.config['google_sheet']['credentials_file'] = self.credentials_var.get()

        if self.config_master.save_config():
            messagebox.showinfo("Success", "Đã lưu cấu hình thành công!")
        else:
            messagebox.showerror("Error", "Lỗi khi lưu cấu hình!")

    def export_config(self):
        """Export cấu hình cho rename_pdf.py"""
        # Tạo config đơn giản cho rename_pdf.py
        export_config = {
            "ai_provider": self.config_master.config['ai_provider'],
            "selected_model": self.config_master.config['models'][self.config_master.config['ai_provider']]['selected'],
            "api_keys": self.config_master.config['api_keys'],
            "google_sheet": self.config_master.config['google_sheet'],
            "python_path": self.config_master.get_selected_python_path()
        }

        export_file = os.path.join(self.config_master.script_dir, 'rename_pdf_config.json')
        try:
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(export_config, f, indent=4, ensure_ascii=False)
            messagebox.showinfo("Success", f"Đã export cấu hình vào {export_file}")
        except Exception as e:
            messagebox.showerror("Error", f"Lỗi khi export: {str(e)}")

    def run(self):
        """Chạy GUI"""
        self.root.mainloop()

def main():
    """Hàm main"""
    try:
        app = ConfigMasterGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("Error", f"Lỗi khởi tạo: {str(e)}")

if __name__ == "__main__":
    main()
