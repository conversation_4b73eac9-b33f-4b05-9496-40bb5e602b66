@echo off
chcp 65001 > nul
echo Khởi động Config Master v<PERSON><PERSON> quyền Administrator...

:: <PERSON><PERSON><PERSON> tra quyền admin
net session >nul 2>&1
if %errorLevel% == 0 (
    echo <PERSON><PERSON> c<PERSON> quyền Administrator.
) else (
    echo <PERSON><PERSON><PERSON> c<PERSON><PERSON> quyền Administrator...
    powershell -Command "Start-Process cmd -ArgumentList '/c cd /d \"%~dp0\" && python config_master.py && pause' -Verb RunAs"
    exit /b
)

:: Ch<PERSON>y config master
cd /d "%~dp0"
python config_master.py
pause
