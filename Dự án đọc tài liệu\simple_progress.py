#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Simple Progress Dialog - <PERSON><PERSON>n bản đơn giản hóa
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
from datetime import datetime

class SimpleProgressDialog:
    def __init__(self, title="Đang xử lý..."):
        self.title = title
        self.root = None
        self.progress_var = None
        self.status_var = None
        self.is_cancelled = False
        self.is_completed = False
        self.current_step = 0
        self.total_steps = 0
        
    def create_window(self):
        """Tạo cửa sổ progress"""
        self.root = tk.Tk()
        self.root.title(self.title)
        self.root.geometry("600x350")
        self.root.resizable(True, True)  # Cho phép resize
        self.root.minsize(500, 300)  # <PERSON><PERSON><PERSON> thước tối thiểu

        # Center window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (300)
        y = (self.root.winfo_screenheight() // 2) - (175)
        self.root.geometry(f"600x350+{x}+{y}")

        # Variables
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar()
        self.status_var.set("Đang khởi tạo...")

        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        title_label = ttk.Label(main_frame, text=self.title,
                               font=("Arial", 12, "bold"))
        title_label.pack(pady=(0, 20))

        # Progress bar frame
        progress_frame = ttk.Frame(main_frame)
        progress_frame.pack(fill=tk.X, pady=(0, 10))

        self.progress_bar = ttk.Progressbar(progress_frame,
                                           variable=self.progress_var,
                                           maximum=100,
                                           mode='determinate')
        self.progress_bar.pack(fill=tk.X, padx=(0, 100))  # Để chỗ cho progress label

        # Progress label
        self.progress_label = ttk.Label(progress_frame, text="0%",
                                       font=("Arial", 10, "bold"))
        self.progress_label.pack(side=tk.RIGHT)

        # Status frame với scrollable text
        status_frame = ttk.LabelFrame(main_frame, text="Trạng thái", padding=10)
        status_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 20))

        # Text widget thay vì Label để có thể scroll và hiển thị nhiều dòng
        self.status_text = tk.Text(status_frame, height=8, wrap=tk.WORD,
                                  font=("Arial", 9), state=tk.DISABLED)

        # Scrollbar cho status text
        status_scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL,
                                        command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=status_scrollbar.set)

        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        status_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        self.cancel_button = ttk.Button(button_frame, text="Hủy",
                                       command=self.cancel)
        self.cancel_button.pack(side=tk.RIGHT)

        # Handle window close
        self.root.protocol("WM_DELETE_WINDOW", self.cancel)
        
    def update_progress(self, percentage, status=""):
        """Update progress"""
        if self.root is None:
            return

        try:
            if percentage is not None:
                self.progress_var.set(percentage)
                self.progress_label.config(text=f"{percentage:.1f}%")

            if status:
                # Thêm timestamp
                timestamp = datetime.now().strftime("%H:%M:%S")
                status_with_time = f"[{timestamp}] {status}"

                # Cập nhật text widget
                self.status_text.config(state=tk.NORMAL)
                self.status_text.insert(tk.END, status_with_time + "\n")
                self.status_text.see(tk.END)  # Scroll to bottom
                self.status_text.config(state=tk.DISABLED)

                # Giữ lại status_var cho compatibility
                self.status_var.set(status)

            self.root.update()
        except:
            pass
    
    def set_completed(self, success=True, message=""):
        """Mark as completed"""
        if self.root is None:
            return

        try:
            self.is_completed = True

            # Update progress bar
            if success:
                self.progress_var.set(100)
                self.progress_label.config(text="100%")

            # Add completion message to text widget
            timestamp = datetime.now().strftime("%H:%M:%S")
            completion_msg = f"[{timestamp}] {'✅ ' if success else '❌ '}{message or ('Hoàn thành!' if success else 'Có lỗi xảy ra!')}"

            self.status_text.config(state=tk.NORMAL)
            self.status_text.insert(tk.END, "\n" + "="*50 + "\n")
            self.status_text.insert(tk.END, completion_msg + "\n")
            self.status_text.insert(tk.END, "="*50 + "\n")
            self.status_text.see(tk.END)
            self.status_text.config(state=tk.DISABLED)

            # Update status_var for compatibility
            self.status_var.set(message or ("Hoàn thành!" if success else "Có lỗi xảy ra!"))

            self.cancel_button.config(text="Đóng")
            self.root.update()
        except:
            pass
    
    def cancel(self):
        """Cancel operation"""
        self.is_cancelled = True
        if self.root:
            try:
                self.root.destroy()
            except:
                pass
    
    def show_and_run(self, work_function):
        """Hiển thị dialog và chạy work function"""
        self.create_window()
        
        # Chạy work function trong thread
        def run_work():
            try:
                work_function(self)
            except Exception as e:
                self.set_completed(False, f"Lỗi: {str(e)}")
        
        thread = threading.Thread(target=run_work, daemon=True)
        thread.start()
        
        # Chạy mainloop
        try:
            self.root.mainloop()
        except:
            pass

class SimpleProgressManager:
    """Manager đơn giản cho progress"""
    
    def __init__(self, title="Đang xử lý..."):
        self.dialog = SimpleProgressDialog(title)
        self.steps = []
        self.current_step = 0
        
    def set_steps(self, steps):
        """Set danh sách steps: [(name, weight), ...]"""
        self.steps = steps
        self.total_weight = sum(weight for _, weight in steps)
        
    def start_step(self, step_index, detail=""):
        """Bắt đầu step"""
        if step_index < len(self.steps):
            self.current_step = step_index
            step_name, _ = self.steps[step_index]
            
            # Calculate progress up to this step
            progress = sum(weight for _, weight in self.steps[:step_index]) / self.total_weight * 100
            
            status = f"Bước {step_index + 1}/{len(self.steps)}: {step_name}"
            if detail:
                status += f" - {detail}"
                
            self.dialog.update_progress(progress, status)
    
    def update_step_progress(self, step_progress, detail=""):
        """Update progress trong step hiện tại"""
        if self.current_step < len(self.steps):
            # Progress của các step đã hoàn thành
            completed_progress = sum(weight for _, weight in self.steps[:self.current_step]) / self.total_weight * 100
            
            # Progress của step hiện tại
            current_weight = self.steps[self.current_step][1]
            current_progress = (step_progress / 100) * (current_weight / self.total_weight * 100)
            
            total_progress = completed_progress + current_progress
            
            step_name, _ = self.steps[self.current_step]
            status = f"Bước {self.current_step + 1}/{len(self.steps)}: {step_name}"
            if detail:
                status += f" - {detail}"
                
            self.dialog.update_progress(total_progress, status)
    
    def complete_step(self, detail=""):
        """Hoàn thành step hiện tại"""
        if self.current_step < len(self.steps):
            step_name, _ = self.steps[self.current_step]
            
            # Progress sau khi hoàn thành step này
            progress = sum(weight for _, weight in self.steps[:self.current_step + 1]) / self.total_weight * 100
            
            status = f"Hoàn thành: {step_name}"
            if detail:
                status += f" - {detail}"
                
            self.dialog.update_progress(progress, status)
    
    def set_completed(self, success=True, message=""):
        """Hoàn thành toàn bộ"""
        self.dialog.set_completed(success, message)
    
    def is_cancelled(self):
        """Kiểm tra có bị hủy không"""
        return self.dialog.is_cancelled
    
    def show_and_run(self, work_function):
        """Hiển thị và chạy work function"""
        self.dialog.show_and_run(work_function)

# Test function
def test_simple_progress():
    """Test simple progress"""
    def work_function(progress_dialog):
        # Simulate work
        steps = [
            ("Khởi tạo", 20),
            ("Xử lý dữ liệu", 40),
            ("Lưu kết quả", 40)
        ]
        
        manager = SimpleProgressManager("Test Simple Progress")
        manager.dialog = progress_dialog  # Use existing dialog
        manager.set_steps(steps)
        
        for i, (step_name, weight) in enumerate(steps):
            if progress_dialog.is_cancelled:
                break
                
            manager.start_step(i, f"Bắt đầu {step_name}")
            
            # Simulate work in step
            for j in range(10):
                if progress_dialog.is_cancelled:
                    break
                time.sleep(0.2)
                sub_progress = (j + 1) * 10
                manager.update_step_progress(sub_progress, f"{sub_progress}% hoàn thành")
            
            manager.complete_step("Xong")
        
        if not progress_dialog.is_cancelled:
            manager.set_completed(True, "Tất cả bước đã hoàn thành!")
        
        # Đợi 2 giây
        time.sleep(2)
        progress_dialog.cancel()
    
    # Chạy test
    dialog = SimpleProgressDialog("Test Simple Progress")
    dialog.show_and_run(work_function)

if __name__ == "__main__":
    test_simple_progress()
