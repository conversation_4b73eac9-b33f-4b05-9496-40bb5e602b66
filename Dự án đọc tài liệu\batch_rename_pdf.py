#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Batch Rename PDF - Xử lý nhiều file PDF cùng lúc
Hỗ trợ 2 chế độ:
1. Xử lý từng file một (như rename_pdf.py)
2. Xử lý từng bước cho tất cả file
"""

import os
import sys
import json
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import time
from datetime import datetime
import logging

# Import từ rename_pdf.py
try:
    from rename_pdf import (
        process_pdf_with_progress, 
        ProgressManager,
        setup_logging,
        ConfigManager
    )
except ImportError as e:
    print(f"Lỗi import từ rename_pdf.py: {e}")
    sys.exit(1)

class BatchProgressManager:
    """Progress manager cho batch processing"""
    
    def __init__(self, title="Batch Processing"):
        self.title = title
        self.dialog = None
        self.current_file = 0
        self.total_files = 0
        self.current_step = 0
        self.total_steps = 0
        self.files = []
        self.mode = "individual"  # "individual" hoặc "batch"
        
    def set_files(self, files, mode="individual"):
        """Đặt danh sách file và chế độ xử lý"""
        self.files = files
        self.total_files = len(files)
        self.mode = mode
        
        if mode == "individual":
            # Mỗi file có 5 bước
            self.total_steps = len(files) * 5
        else:
            # Batch mode: 5 bước cho tất cả file
            self.total_steps = 5
            
    def start_file(self, file_index, filename):
        """Bắt đầu xử lý file"""
        self.current_file = file_index
        if self.dialog:
            status = f"File {file_index + 1}/{self.total_files}: {filename}"
            if self.mode == "individual":
                progress = (file_index / self.total_files) * 100
            else:
                progress = 0
            self.dialog.update_progress(progress, status)
            
    def start_step(self, step_index, detail=""):
        """Bắt đầu bước xử lý"""
        self.current_step = step_index
        if self.dialog:
            if self.mode == "individual":
                # Progress cho từng file
                file_progress = (self.current_file / self.total_files) * 100
                step_progress = (step_index / 5) * (100 / self.total_files)
                total_progress = file_progress + step_progress
            else:
                # Progress cho batch
                total_progress = (step_index / 5) * 100
                
            status = f"Bước {step_index + 1}/5: {detail}"
            if self.mode == "batch":
                status += f" (Tất cả {self.total_files} file)"
                
            self.dialog.update_progress(total_progress, status)
            
    def update_step_progress(self, step_progress, detail=""):
        """Cập nhật tiến độ trong bước"""
        if self.dialog:
            if self.mode == "individual":
                file_progress = (self.current_file / self.total_files) * 100
                current_step_progress = (step_progress / 100) * (100 / self.total_files / 5)
                step_base_progress = (self.current_step / 5) * (100 / self.total_files)
                total_progress = file_progress + step_base_progress + current_step_progress
            else:
                step_base_progress = (self.current_step / 5) * 100
                current_step_progress = (step_progress / 100) * (100 / 5)
                total_progress = step_base_progress + current_step_progress
                
            self.dialog.update_progress(total_progress, detail)
            
    def complete_step(self, detail=""):
        """Hoàn thành bước"""
        if self.dialog:
            self.dialog.update_progress(None, f"Hoàn thành: {detail}")
            
    def set_completed(self, success=True, message=""):
        """Hoàn thành toàn bộ"""
        if self.dialog:
            self.dialog.set_completed(success, message)
            
    def is_cancelled(self):
        """Kiểm tra có bị hủy không"""
        return self.dialog.is_cancelled if self.dialog else False

class BatchRenameGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Batch Rename PDF - Xử lý nhiều file PDF")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        self.selected_files = []
        self.processing_mode = tk.StringVar(value="individual")
        
        self.create_gui()
        
    def create_gui(self):
        """Tạo giao diện"""
        # File selection frame
        file_frame = ttk.LabelFrame(self.root, text="Chọn file PDF", padding=10)
        file_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # File list
        list_frame = ttk.Frame(file_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        # Treeview for files
        columns = ('Filename', 'Path', 'Size')
        self.file_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=12)
        
        for col in columns:
            self.file_tree.heading(col, text=col)
            if col == 'Filename':
                self.file_tree.column(col, width=300)
            elif col == 'Path':
                self.file_tree.column(col, width=400)
            else:
                self.file_tree.column(col, width=100)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_tree.yview)
        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.file_tree.xview)
        self.file_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        self.file_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        list_frame.grid_rowconfigure(0, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)
        
        # File buttons
        file_button_frame = ttk.Frame(file_frame)
        file_button_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(file_button_frame, text="Thêm file", command=self.add_files).pack(side=tk.LEFT, padx=5)
        ttk.Button(file_button_frame, text="Thêm thư mục", command=self.add_folder).pack(side=tk.LEFT, padx=5)
        ttk.Button(file_button_frame, text="Xóa file", command=self.remove_files).pack(side=tk.LEFT, padx=5)
        ttk.Button(file_button_frame, text="Xóa tất cả", command=self.clear_files).pack(side=tk.LEFT, padx=5)
        
        # File count
        self.file_count_var = tk.StringVar(value="0 file được chọn")
        ttk.Label(file_button_frame, textvariable=self.file_count_var).pack(side=tk.RIGHT, padx=10)
        
        # Processing mode frame
        mode_frame = ttk.LabelFrame(self.root, text="Chế độ xử lý", padding=10)
        mode_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Radiobutton(mode_frame, text="Xử lý từng file một (như rename_pdf.py)", 
                       variable=self.processing_mode, value="individual").pack(anchor=tk.W)
        ttk.Radiobutton(mode_frame, text="Xử lý từng bước cho tất cả file cùng lúc", 
                       variable=self.processing_mode, value="batch").pack(anchor=tk.W)
        
        # Description
        desc_text = """
Chế độ 1 - Xử lý từng file một:
• Xử lý hoàn toàn file 1, sau đó chuyển sang file 2, v.v.
• Phù hợp khi muốn xem kết quả từng file ngay lập tức

Chế độ 2 - Xử lý từng bước cho tất cả file:
• Bước 1: Chuyển đổi TẤT CẢ PDF thành ảnh
• Bước 2: OCR TẤT CẢ ảnh
• Bước 3: Trích xuất thông tin TẤT CẢ file với AI
• Bước 4: Đổi tên TẤT CẢ file
• Bước 5: Lưu TẤT CẢ vào Google Sheet
• Phù hợp khi xử lý số lượng lớn file, tối ưu hóa tài nguyên
        """
        
        desc_label = ttk.Label(mode_frame, text=desc_text, justify=tk.LEFT, 
                              font=("Arial", 9), foreground="gray")
        desc_label.pack(anchor=tk.W, pady=5)
        
        # Control buttons
        control_frame = ttk.Frame(self.root)
        control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.start_button = ttk.Button(control_frame, text="Bắt đầu xử lý", 
                                      command=self.start_processing, state=tk.DISABLED)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(control_frame, text="Thoát", command=self.root.quit).pack(side=tk.RIGHT, padx=5)
        
    def add_files(self):
        """Thêm file PDF"""
        files = filedialog.askopenfilenames(
            title="Chọn file PDF",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        
        for file_path in files:
            if file_path not in self.selected_files:
                self.selected_files.append(file_path)
                
        self.update_file_list()
        
    def add_folder(self):
        """Thêm tất cả PDF trong thư mục"""
        folder = filedialog.askdirectory(title="Chọn thư mục chứa PDF")
        
        if folder:
            for root, dirs, files in os.walk(folder):
                for file in files:
                    if file.lower().endswith('.pdf'):
                        file_path = os.path.join(root, file)
                        if file_path not in self.selected_files:
                            self.selected_files.append(file_path)
                            
        self.update_file_list()
        
    def remove_files(self):
        """Xóa file đã chọn"""
        selection = self.file_tree.selection()
        if selection:
            for item in selection:
                values = self.file_tree.item(item)['values']
                file_path = values[1]  # Path column
                if file_path in self.selected_files:
                    self.selected_files.remove(file_path)
                    
        self.update_file_list()
        
    def clear_files(self):
        """Xóa tất cả file"""
        if messagebox.askyesno("Confirm", "Xóa tất cả file đã chọn?"):
            self.selected_files.clear()
            self.update_file_list()
            
    def update_file_list(self):
        """Cập nhật danh sách file"""
        # Clear treeview
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)
            
        # Add files
        for file_path in self.selected_files:
            if os.path.exists(file_path):
                filename = os.path.basename(file_path)
                try:
                    size = os.path.getsize(file_path)
                    size_str = f"{size / 1024 / 1024:.1f} MB"
                except:
                    size_str = "Unknown"
                    
                self.file_tree.insert('', tk.END, values=(filename, file_path, size_str))
                
        # Update count and button state
        count = len(self.selected_files)
        self.file_count_var.set(f"{count} file được chọn")
        self.start_button.config(state=tk.NORMAL if count > 0 else tk.DISABLED)
        
    def start_processing(self):
        """Bắt đầu xử lý"""
        if not self.selected_files:
            messagebox.showwarning("Warning", "Chưa chọn file nào!")
            return
            
        mode = self.processing_mode.get()
        
        # Confirm
        mode_text = "từng file một" if mode == "individual" else "từng bước cho tất cả file"
        if not messagebox.askyesno("Confirm", 
                                  f"Bắt đầu xử lý {len(self.selected_files)} file theo chế độ {mode_text}?"):
            return
            
        # Start processing in thread
        def process_thread():
            try:
                if mode == "individual":
                    self.process_individual()
                else:
                    self.process_batch()
            except Exception as e:
                messagebox.showerror("Error", f"Lỗi xử lý: {str(e)}")
                
        threading.Thread(target=process_thread, daemon=True).start()
        
    def process_individual(self):
        """Xử lý từng file một"""
        from simple_progress import SimpleProgressDialog
        
        def work_function(progress_dialog):
            progress_manager = BatchProgressManager("Xử lý từng file một")
            progress_manager.dialog = progress_dialog
            progress_manager.set_files(self.selected_files, "individual")
            
            success_count = 0
            error_files = []
            
            for i, file_path in enumerate(self.selected_files):
                if progress_manager.is_cancelled():
                    break
                    
                filename = os.path.basename(file_path)
                progress_manager.start_file(i, filename)
                
                try:
                    # Tạo progress manager cho file này
                    file_progress = ProgressManager(f"Xử lý {filename}")
                    file_progress.dialog = progress_dialog
                    
                    # Định nghĩa các bước
                    steps = [
                        ("Kiểm tra file và cấu hình", 10),
                        ("Chuyển đổi PDF thành ảnh", 20),
                        ("OCR văn bản", 30),
                        ("Trích xuất thông tin với AI", 25),
                        ("Lưu kết quả", 15)
                    ]
                    
                    file_progress.set_steps(steps)
                    
                    # Xử lý file
                    process_pdf_with_progress(file_path, file_progress)
                    success_count += 1
                    
                except Exception as e:
                    error_files.append((filename, str(e)))
                    
            # Kết quả
            if not progress_manager.is_cancelled():
                result_msg = f"Hoàn thành!\n"
                result_msg += f"Thành công: {success_count}/{len(self.selected_files)} file\n"
                if error_files:
                    result_msg += f"Lỗi: {len(error_files)} file\n"
                    result_msg += "\nFile lỗi:\n"
                    for filename, error in error_files[:5]:  # Chỉ hiển thị 5 file đầu
                        result_msg += f"• {filename}: {error}\n"
                        
                progress_manager.set_completed(True, result_msg)
                
        dialog = SimpleProgressDialog("Batch Processing - Individual Mode")
        dialog.show_and_run(work_function)
        
    def process_batch(self):
        """Xử lý từng bước cho tất cả file"""
        # Placeholder - sẽ implement chi tiết sau
        messagebox.showinfo("Info", "Chế độ batch processing sẽ được implement chi tiết sau")
        
    def run(self):
        """Chạy GUI"""
        self.root.mainloop()

def main():
    """Hàm main"""
    try:
        app = BatchRenameGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("Error", f"Lỗi khởi tạo: {str(e)}")

if __name__ == "__main__":
    main()
