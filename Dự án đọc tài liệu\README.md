# Dự án đọc tài liệu - PDF OCR và Tóm tắt

Ứng dụng Python toàn diện để đọc và xử lý file PDF, thực hiện OCR với Google Vision API, tạo tóm tắt văn bản và quản lý dữ liệu với Google Sheet.

## 🚀 Tính năng chính

### 1. Xử lý PDF
- **OCR PDF**: Sử dụng Google Vision API để chuyển đổi PDF thành text
- **Tóm tắt văn bản**: Sử dụng AI (Gemini, OpenAI, Grok) để tóm tắt nội dung
- **Trích xuất thông tin**: Tự động lấy ngày ký, số công văn, tiêu đề
- **Xuất file Word**: Tạo file .docx với nội dung đã xử lý
- **Đổi tên file**: Tự động đặt tên theo format chuẩn
- **Ghi Google Sheet**: <PERSON><PERSON><PERSON> thông tin vào bảng tính Google

### 2. Quản lý phiên bản Python
- **Tự động phát hiện**: <PERSON><PERSON><PERSON> tất cả phiên bản Python đã cài đặt
- **Quản lý phiên bản**: Thêm, xóa, chọn phiên bản Python
- **Chạy script**: Chọn phiên bản Python để chạy từng script

### 3. Context Menu cho PDF
- **Chuột phải PDF**: Thêm menu "Đổi tên PDF và lưu Google Sheet"
- **Xử lý nhanh**: Chạy trực tiếp từ Windows Explorer
- **Tự động hóa**: Không cần mở ứng dụng chính

### 4. Giao diện quản lý dự án
- **Project Launcher**: Giao diện trung tâm để quản lý tất cả script
- **Config Manager**: Cấu hình API keys và Google Sheet
- **Python Manager**: Quản lý phiên bản Python

## 📋 Yêu cầu hệ thống

- **Python**: 3.8 trở lên
- **Hệ điều hành**: Windows (có hỗ trợ Linux/Mac với một số điều chỉnh)
- **API Keys**: Google Cloud Vision, Google AI (Gemini), OpenAI (tùy chọn), Grok (tùy chọn)
- **Google Sheet**: Credentials file JSON

## 🛠️ Cài đặt

### 1. Cài đặt thư viện
```bash
pip install -r requirements.txt
```

### 2. Cấu hình API
1. Chạy **Config Manager**:
   ```bash
   python config_manager.py
   ```
2. Nhập các API keys cần thiết
3. Cấu hình Google Sheet URL và credentials file

### 3. Cấu hình tổng hợp (Khuyến nghị)
1. Chạy **Config Master** với quyền Administrator:
   ```bash
   run_config_master_admin.bat
   ```
   Hoặc:
   ```bash
   python config_master.py
   ```
2. Cấu hình tất cả trong một giao diện:
   - **AI Configuration**: Chọn AI provider và nhập API keys
   - **Python Versions**: Quản lý phiên bản Python
   - **Libraries**: Kiểm tra và cài đặt thư viện thiếu
   - **Context Menu**: Đăng ký/hủy đăng ký menu chuột phải
   - **Google Sheet**: Cấu hình Google Sheet và credentials

### 3. Đăng ký Context Menu (cách cũ)
1. Chạy với quyền Administrator:
   ```bash
   register_context_menu_admin.bat
   ```
   Hoặc:
   ```bash
   python register_pdf_context_menu.py
   ```

## 🎯 Cách sử dụng

### Phương pháp 1: Sử dụng Project Launcher (Khuyến nghị)

**Có hiển thị terminal:**
```bash
python project_launcher.py
```
Hoặc double-click: `run_launcher.bat`

**Ẩn terminal (Khuyến nghị):**
```bash
pythonw project_launcher.pyw
```
Hoặc double-click: `run_launcher_hidden.bat`

**Trong Project Launcher:**
1. Chọn phiên bản Python (nếu có nhiều phiên bản)
2. Chọn script muốn chạy từ danh sách
3. Double-click hoặc nhấn "Chạy Script"

### Phương pháp 2: Chạy trực tiếp từng ứng dụng

#### Ứng dụng chính
**Có terminal:**
```bash
python main.py
```
**Ẩn terminal:**
```bash
pythonw main.pyw
```

#### Config Master (Khuyến nghị - Tổng hợp tất cả cấu hình)
```bash
run_config_master_admin.bat
```
Hoặc:
```bash
python config_master.py
```

#### Batch Rename PDF (Xử lý nhiều file)
```bash
run_batch_rename.bat
```
Hoặc:
```bash
python batch_rename_pdf.py
```

#### Quản lý cấu hình (cũ)
```bash
python config_manager.py
```

#### Quản lý phiên bản Python (cũ)
```bash
python python_version_manager.py
```

#### Đăng ký Context Menu (cũ)
```bash
python register_pdf_context_menu.py
```

### Phương pháp 3: Sử dụng Context Menu (Ẩn terminal)
1. Đăng ký context menu (chỉ cần làm 1 lần)
2. Nhấn chuột phải vào file PDF
3. Chọn "Đổi tên PDF và lưu Google Sheet"
4. **Không hiển thị terminal** - chỉ có progress dialog

### Phương pháp 4: Sử dụng Shortcuts
1. Chạy `python create_shortcuts.py` để tạo shortcuts
2. Double-click shortcuts trên Desktop
3. **Không hiển thị terminal** - giao diện sạch sẽ

## 📁 Cấu trúc dự án

```
Dự án đọc tài liệu/
├── main.py                          # Ứng dụng chính
├── config_manager.py                # Quản lý cấu hình (cũ)
├── config_master.py                 # 🆕 Quản lý cấu hình tổng hợp
├── rename_pdf.py                    # Script đổi tên PDF đơn lẻ
├── batch_rename_pdf.py              # 🆕 Script xử lý nhiều PDF
├── python_version_manager.py        # Quản lý phiên bản Python (cũ)
├── register_pdf_context_menu.py     # Đăng ký context menu (cũ)
├── project_launcher.py              # Launcher chính
├── config.py                        # Cấu hình cơ bản (cũ)
├── simple_progress.py               # 🔄 Giao diện progress cải tiến
├── requirements.txt                 # Danh sách thư viện
├── run_launcher.bat                 # Chạy launcher
├── run_config_master_admin.bat      # 🆕 Chạy config master (admin)
├── run_batch_rename.bat             # 🆕 Chạy batch rename
├── run_python_manager.bat           # Chạy Python manager
├── register_context_menu_admin.bat  # Đăng ký context menu (admin)
├── config.json                      # File cấu hình chính (cũ)
├── master_config.json               # 🆕 File cấu hình tổng hợp
├── rename_pdf_config.json           # 🆕 Cấu hình cho rename_pdf.py
├── python_versions.json             # Cấu hình phiên bản Python
├── context_menu_config.json         # Cấu hình context menu
├── launcher_config.json             # Cấu hình launcher
└── Json/                            # Thư mục chứa credentials
    └── *.json                       # Google credentials files
```

## ⚙️ Cấu hình

### API Keys cần thiết:
1. **Google Cloud Vision API**: Để OCR PDF
2. **Google AI API (Gemini)**: Để tóm tắt và trích xuất thông tin
3. **OpenAI API** (tùy chọn): Alternative AI provider
4. **Grok API** (tùy chọn): Alternative AI provider

### Google Sheet:
1. Tạo Google Sheet để lưu dữ liệu
2. Tạo Service Account và download credentials JSON
3. Share Google Sheet với email của Service Account

## 🔧 Tính năng nâng cao

### Quản lý phiên bản Python
- Tự động phát hiện Python từ PATH, Registry, thư mục phổ biến
- Hỗ trợ thêm phiên bản Python tùy chỉnh
- Chạy script với phiên bản Python được chọn

### Context Menu
- Đăng ký/hủy đăng ký menu chuột phải
- Tùy chỉnh tên hiển thị và icon
- Chạy trực tiếp từ Windows Explorer

### Project Launcher
- Giao diện trung tâm để quản lý tất cả script
- Lưu lịch sử script đã chạy
- Quản lý phiên bản Python cho từng script

## 🐛 Xử lý sự cố

### Lỗi phổ biến:
1. **Thiếu API Key**: Chạy `config_manager.py` để cấu hình
2. **Lỗi Google Vision**: Kiểm tra credentials file JSON
3. **Lỗi Context Menu**: Chạy với quyền Administrator
4. **Lỗi Python**: Sử dụng Python Version Manager để kiểm tra

### Lỗi API Keys:
- **"Invalid API Key"**: Chạy `python check_api_keys.py` để setup
- **Thiếu API Key**: Cấu hình ít nhất 1 API key để sử dụng

### Lỗi OpenAI API (Phiên bản mới):
- **"unexpected keyword argument"**: Chạy `python fix_openai_config.py`
- **"no attribute 'chat'"**: Chạy `python fix_openai_chat_error.py`
- **Cập nhật OpenAI**: `pip install --upgrade openai`

### Lỗi Grok API:
- **"unexpected keyword argument 'proxies'"**: Chạy `python fix_grok_proxies_error.py`
- **Cập nhật Grok**: `pip install --upgrade groq`

### Test tất cả APIs:
- **Test APIs**: `python test_all_apis_latest.py`
- **Kiểm tra version**: `python check_openai_version.py`

### Debug và Log:
- **Debug Log Viewer**: Chạy `view_debug_log.py` hoặc nhấn "Xem Logs" trong Project Launcher
- **Log Files**:
  - `rename_pdf_debug.log`: Chi tiết quá trình xử lý PDF
  - `debug.log`: Log của ứng dụng chính
  - `config_debug.log`: Log cấu hình
- **Tính năng Debug Log Viewer**:
  - Xem real-time log với syntax highlighting
  - Filter log theo từ khóa
  - Auto-refresh mỗi 5 giây
  - Clear log và save log
  - Hiển thị timestamp, log level với màu sắc khác nhau
- **Sử dụng "Test Connection"** trong Config Manager để kiểm tra kết nối

## 📝 Lưu ý

- **Quyền Administrator**: Cần để đăng ký context menu
- **Chất lượng PDF**: PDF có chất lượng tốt sẽ cho kết quả OCR chính xác hơn
- **Kết nối Internet**: Cần để sử dụng Google APIs
- **Google Sheet**: Cần share với Service Account email

## 🆕 Cập nhật mới

### Version 3.0 (Latest):
- ✅ **Config Master**: Giao diện tổng hợp quản lý tất cả cấu hình
  - Gộp chức năng từ config.py, config_manager.py, python_version_manager.py, register_pdf_context_menu.py
  - Giao diện GUI với tabs riêng biệt cho từng loại cấu hình
  - Tự động kiểm tra và cài đặt thư viện thiếu
  - Quản lý phiên bản Python và đăng ký Context Menu
  - Export cấu hình cho rename_pdf.py
- ✅ **Batch Rename PDF**: Xử lý nhiều file PDF cùng lúc
  - Chế độ 1: Xử lý từng file một (như rename_pdf.py)
  - Chế độ 2: Xử lý từng bước cho tất cả file cùng lúc
  - Giao diện chọn file với drag & drop
  - Hiển thị tiến độ chi tiết cho từng chế độ
- ✅ **Giao diện Progress cải tiến**:
  - Cửa sổ có thể resize và điều chỉnh kích thước
  - Text area với scrollbar thay vì label cố định
  - Hiển thị timestamp cho mỗi bước
  - Không bị mất chữ do khung nhỏ

### Version 2.2:
- ✅ **Latest API Compatibility**: Tương thích với phiên bản mới nhất
  - OpenAI v1.50+: gpt-4.1, gpt-4.1-mini, gpt-4.1-nano, o4-mini, o3, o3-mini
  - Gemini v0.8+: gemini-2.0-flash, gemini-2.5-pro-preview
  - Grok v0.11+: grok-3, grok-3-vision
- ✅ **Auto Version Detection**: Tự động phát hiện và tương thích với API versions
- ✅ **Enhanced Error Handling**: Thông báo lỗi cụ thể và hướng dẫn fix
- ✅ **Hidden Terminal Mode**: Chạy ứng dụng mà không hiển thị terminal
- ✅ **Fix Tools**: Scripts tự động sửa lỗi và test APIs

### Version 2.1:
- ✅ **Progress Dialog**: Giao diện popup với thanh tiến trình thay vì terminal
- ✅ **Model Updates**: Cập nhật model mới nhất cho OpenAI và Grok
  - OpenAI: gpt-4o, gpt-4o-mini, o1-preview, o1-mini
  - Grok: grok-beta, grok-vision-beta, grok-2-1212
- ✅ **Vision AI**: Trích xuất thông tin trực tiếp từ ảnh PDF
- ✅ **Smart Progress**: Hiển thị % hoàn thành cho từng bước
- ✅ **Cancel Support**: Có thể hủy quá trình xử lý
- ✅ **Real-time Status**: Cập nhật trạng thái và chi tiết real-time

### Version 2.0:
- ✅ Thêm Python Version Manager
- ✅ Thêm Context Menu cho PDF
- ✅ Thêm Project Launcher
- ✅ Cải thiện Config Manager
- ✅ Hỗ trợ nhiều AI providers (Gemini, OpenAI, Grok)
- ✅ Tự động phát hiện phiên bản Python
- ✅ Giao diện quản lý dự án tổng thể