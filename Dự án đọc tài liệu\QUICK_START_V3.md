# 🚀 QUICK START V3.0 - Hướng dẫn nhanh

## 📋 Chuẩn bị

### 1. Cài đặt Python
- Tải Python 3.8+ từ [python.org](https://python.org)
- <PERSON><PERSON><PERSON> b<PERSON><PERSON> chọn "Add Python to PATH" khi cài đặt

## 🔥 Cách nhanh nhất - Config Master (Khuyến nghị)

### Bước 1: Ch<PERSON>y Config Master với quyền Admin
```bash
run_config_master_admin.bat
```

### Bước 2: Cấu hình tất cả trong một giao diện
**Config Master sẽ tự động:**
- ✅ Kiểm tra và cài đặt thư viện thiếu
- ✅ Phát hiện phiên bản Python
- ✅ Cấu hình API Keys
- ✅ Đăng ký Context Menu
- ✅ Cấu hình Google Sheet
- ✅ Export cấu hình cho rename_pdf.py

**Các tab trong Config Master:**
1. **AI Configuration**: Chọn AI provider (Google/OpenAI/Grok) và nhập API keys
2. **Python Versions**: Chọn phiên bản Python sử dụng
3. **Libraries**: Kiểm tra và cài đặt thư viện từ requirements.txt
4. **Context Menu**: Đăng ký/hủy đăng ký menu chuột phải
5. **Google Sheet**: Cấu hình URL và credentials file

### Bước 3: Lưu và Export
- Nhấn **"Save Configuration"** để lưu cấu hình
- Nhấn **"Export for rename_pdf.py"** để tạo file cấu hình

## 🎯 Sử dụng

### 🔥 Xử lý nhiều file - Batch Rename PDF
```bash
run_batch_rename.bat
```

**Tính năng:**
- **Thêm file**: Nhấn "Thêm file" hoặc "Thêm thư mục"
- **2 chế độ xử lý**:
  - **Từng file một**: Xử lý hoàn toàn file 1 → file 2 → file 3...
  - **Từng bước cho tất cả**: PDF→ảnh (tất cả) → OCR (tất cả) → AI (tất cả)...
- **Giao diện resizable**: Có thể điều chỉnh kích thước
- **Progress chi tiết**: Hiển thị tiến độ cho từng chế độ

### Context Menu (File đơn lẻ)
1. **Đăng ký** (đã làm trong Config Master)
2. **Sử dụng**: Chuột phải vào file PDF → Chọn "Đổi tên PDF và lưu Google Sheet"

### Project Launcher (Cách cũ)
```bash
run_launcher_hidden.bat
```

### Chạy trực tiếp (Cách cũ)
```bash
python rename_pdf.py "path/to/file.pdf"
```

## 🆕 Tính năng mới Version 3.0

### ✨ Config Master
- **Giao diện tổng hợp**: Tất cả cấu hình trong một ứng dụng
- **Tự động cài đặt thư viện**: Kiểm tra và cài đặt requirements.txt
- **Quản lý Python**: Phát hiện và quản lý phiên bản Python
- **Context Menu**: Đăng ký/hủy đăng ký ngay trong giao diện
- **Export config**: Tạo file cấu hình cho rename_pdf.py

### ✨ Batch Rename PDF
- **Xử lý nhiều file**: Chọn nhiều PDF cùng lúc
- **2 chế độ xử lý**:
  - Individual: Từng file một (như rename_pdf.py)
  - Batch: Từng bước cho tất cả file
- **Giao diện drag & drop**: Dễ dàng thêm file
- **Progress chi tiết**: Hiển thị tiến độ cho từng chế độ

### ✨ Giao diện Progress cải tiến
- **Resizable**: Có thể điều chỉnh kích thước cửa sổ
- **Text area**: Scrollable text thay vì label cố định
- **Timestamp**: Hiển thị thời gian cho mỗi bước
- **Không mất chữ**: Text không bị cắt do khung nhỏ

## 🔧 API Keys cần thiết

### Google AI (Gemini) - Miễn phí
- Lấy từ: https://aistudio.google.com/app/apikey
- **Khuyến nghị**: Sử dụng làm provider chính

### OpenAI - Trả phí (tùy chọn)
- Lấy từ: https://platform.openai.com/api-keys
- Models: gpt-4o, gpt-4o-mini, o1-preview, o1-mini

### Grok (xAI) - Trả phí (tùy chọn)
- Lấy từ: https://console.x.ai/team/api-keys
- Models: grok-beta, grok-vision-beta, grok-2

*Ít nhất cần 1 API key để sử dụng ứng dụng*

## 🔧 Google Sheet Setup

### 1. Tạo Google Sheet
- Tạo Google Sheet mới
- Copy URL của Sheet

### 2. Tạo Service Account
1. Vào Google Cloud Console
2. Tạo Service Account
3. Download credentials JSON file
4. Đặt file JSON vào thư mục dự án

### 3. Share Sheet
- Share Google Sheet với email của Service Account
- Cấp quyền "Editor"

## 🐛 Xử lý sự cố

### Lỗi thư viện
- Config Master sẽ tự động kiểm tra và cài đặt
- Hoặc chạy: `pip install -r requirements.txt`

### Lỗi API Key
```bash
python check_api_keys.py
```

### Lỗi OpenAI mới
```bash
python fix_openai_config.py
```

### Test tất cả APIs
```bash
python test_all_apis_latest.py
```

### Xem logs
```bash
python view_debug_log.py
```

## 📁 File cấu hình mới

- **`master_config.json`**: Cấu hình Config Master
- **`rename_pdf_config.json`**: Cấu hình cho rename_pdf.py
- **`run_config_master_admin.bat`**: Chạy Config Master với admin
- **`run_batch_rename.bat`**: Chạy Batch Rename PDF

## 📞 Hỗ trợ

- **README.md**: Hướng dẫn chi tiết
- **QUICK_START.md**: Hướng dẫn cũ
- **Debug logs**: Kiểm tra file `rename_pdf_debug.log`
- **Config files**: Các file `.json` trong thư mục dự án

## 🎉 Tóm tắt workflow

1. **Chạy Config Master** → Cấu hình tất cả
2. **Chọn cách sử dụng**:
   - **Nhiều file**: Batch Rename PDF
   - **File đơn**: Context Menu hoặc rename_pdf.py
3. **Xử lý và nhận kết quả**!
